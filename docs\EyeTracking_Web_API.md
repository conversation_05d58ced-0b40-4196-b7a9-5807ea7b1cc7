# 眼动追踪Web调用API文档

## 概述

本文档描述调用Android原生的眼动追踪功能。

## 前提条件

- Android应用需要集成眼动追踪模块
- WebView需要启用JavaScript支持
- 需要注册JavaScriptInterface接口

## JavaScript接口

### 1. 启动眼动追踪

```javascript
// 启动眼动追踪
android.startGazeTracking();
```

**日志输出示例：**
```
🚀 JavaScript调用: startGazeTracking - 启动眼动追踪
  调用时间: 1703123456789
  监听器状态: 已设置
🚀 HrvActivity - 收到启动眼动追踪请求
正在调用GazeTrackingManager.startGazeTracking...
眼动追踪启动结果: true
✓ 眼动追踪启动成功，已通知Web页面
```

### 2. 停止眼动追踪

```javascript
// 停止眼动追踪
android.stopGazeTracking();
```

**日志输出示例：**
```
🛑 JavaScript调用: stopGazeTracking - 停止眼动追踪
  调用时间: 1703123456789
  监听器状态: 已设置
🛑 HrvActivity - 收到停止眼动追踪请求
正在调用GazeTrackingManager.stopGazeTracking...
✓ 眼动追踪已停止
已通知Web页面眼动追踪停止
```

### 3. 设置眼动追踪状态

```javascript
// 开启眼动追踪
android.setGazeTrackingEnabled(true);

// 关闭眼动追踪
android.setGazeTrackingEnabled(false);
```

**日志输出示例：**
```
⚙️ JavaScript调用: setGazeTrackingEnabled - 设置眼动追踪状态
  启用状态: true
  调用时间: 1703123456789
  监听器状态: 已设置
⚙️ HrvActivity - 收到眼动追踪状态变化请求: true
状态设置为启用，调用启动方法
```

## 回调函数

Web页面需要实现以下回调函数来接收Android端的状态通知：

### 1. 眼动追踪启动状态回调

```javascript
// 眼动追踪启动状态回调
function onGazeTrackingStarted(success) {
    if (success) {
        console.log("眼动追踪启动成功");
        // 更新UI状态
        updateUIForGazeTrackingActive();
    } else {
        console.log("眼动追踪启动失败");
        // 显示错误提示
        showErrorMessage("眼动追踪启动失败，请检查设备状态");
    }
}
```

### 2. 眼动追踪停止状态回调

```javascript
// 眼动追踪停止状态回调
function onGazeTrackingStopped() {
    console.log("眼动追踪已停止");
    // 更新UI状态
    updateUIForGazeTrackingInactive();
}
```

## 使用示例

### 完整的Web页面示例

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>眼动追踪控制示例</title>
    <style>
        .btn {
            padding: 10px 20px;
            margin: 10px;
            font-size: 16px;
            cursor: pointer;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>眼动追踪控制面板</h1>
    
    <div class="status">
        <p>当前状态: <span id="statusText">未知</span></p>
    </div>
    
    <button class="btn" onclick="startEyeTracking()">启动眼动追踪</button>
    <button class="btn" onclick="stopEyeTracking()">停止眼动追踪</button>
    <button class="btn" onclick="toggleEyeTracking()">切换眼动追踪</button>
    
    <script>
        let isEyeTrackingActive = false;
        
        function startEyeTracking() {
            console.log("请求启动眼动追踪");
            android.startGazeTracking();
        }
        
        function stopEyeTracking() {
            console.log("请求停止眼动追踪");
            android.stopGazeTracking();
        }
        
        function toggleEyeTracking() {
            console.log("切换眼动追踪状态");
            android.setGazeTrackingEnabled(!isEyeTrackingActive);
        }
        
        // 眼动追踪启动状态回调
        function onGazeTrackingStarted(success) {
            if (success) {
                console.log("眼动追踪启动成功");
                isEyeTrackingActive = true;
                updateStatusText("眼动追踪已启动");
            } else {
                console.log("眼动追踪启动失败");
                isEyeTrackingActive = false;
                updateStatusText("眼动追踪启动失败");
                alert("眼动追踪启动失败，请检查设备状态");
            }
        }
        
        // 眼动追踪停止状态回调
        function onGazeTrackingStopped() {
            console.log("眼动追踪已停止");
            isEyeTrackingActive = false;
            updateStatusText("眼动追踪已停止");
        }
        
        function updateStatusText(text) {
            document.getElementById("statusText").textContent = text;
        }
        
        // 页面加载完成后初始化状态
        window.onload = function() {
            updateStatusText("准备就绪");
        };
    </script>
</body>
</html>
```

## 注意事项

1. **权限要求**: 眼动追踪需要相机权限，请确保应用已获得相机权限。

2. **设备兼容性**: 眼动追踪功能需要特定的硬件支持，不是所有设备都支持。

3. **性能考虑**: 眼动追踪是资源密集型操作，可能会影响设备性能和电池寿命。

4. **错误处理**: 建议在Web页面中添加适当的错误处理逻辑，以应对启动失败的情况。

5. **生命周期管理**: 建议在页面销毁或不需要时及时停止眼动追踪，以节约资源。

## 调试技巧

1. 使用浏览器开发者工具查看JavaScript控制台输出
2. 检查Android端的Log输出
3. 确保JavaScriptInterface正确注册
4. 验证WebView的JavaScript支持已启用

## 版本兼容性

- Android API Level: 21+
- WebView版本: 建议使用最新版本
- JavaScript ES5+支持
