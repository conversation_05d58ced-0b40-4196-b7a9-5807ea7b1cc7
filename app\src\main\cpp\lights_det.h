#ifndef LIGHTS_DET_H
#define LIGHTS_DET_H

#include "utils.h"

#define lights_det_net_w 128
#define lights_det_net_h 128

#define iris_seg_mask_threshold 0.6
#define pupil_seg_mask_threshold 0.6

#define det_points_prob_threshold 0.4
#define det_points_dist_threshold 8

class lightsDetect
{
public:
    lightsDetect();
    ~lightsDetect();
    void load_model(char* det_model_path, char* seg_model_path, bool& model_isready);
    void recognize(const cv::Mat& src, cv::Rect& iris_rect, std::vector<cv::Point2f> & lights_point_list, cv::Point2f & pupil_cent_point);

private:
    rknn_context light_model_ctx;
    rknn_context pupil_model_ctx;
    rknn_input *data_inputs;
    rknn_output *det_outputs;
    rknn_output *seg_outputs;
};

#endif //LIGHTS_DET_H