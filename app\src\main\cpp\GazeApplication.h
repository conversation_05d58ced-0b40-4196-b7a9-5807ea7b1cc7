//
// Created by knight on 2024/12/27.
//

#ifndef MIT_DD_DEMO_GAZEAPPLICATION_H
#define MIT_DD_DEMO_GAZEAPPLICATION_H
#include <android/native_window_jni.h>
#include "Blur.h"
#include "reading.h"
#include "app_stare.h"
#include "app_glance.h"
#include "app_follow.h"
#include "utils.h"

using namespace std;
using namespace chrono;

typedef std::chrono::high_resolution_clock Clock;

/**
 * 眼动的相关应用
 */
class GazeApplication {
public:
    GazeApplication();
    ~GazeApplication();

private:
    PqBlur pqblur_model = PqBlur(2.5);                 // 虚化处理模型
    ReadingPractice reading_model;                     // 阅读训练模型
    GazeStare stare_model;                             // 眼动检查——注视
    GazeGlance glance_model;                           // 眼动检查——扫视
    GazeFollow follow_model;                           // 眼动检查——跟随

    int app_mode;                                      // 默认不启动应用
    bool app_runing;                                   // 运行状态
    bool finish_flag;                                  // 应用过程是否完成

public:
    // Surface
    pthread_mutex_t mutex{}; // 互斥锁
    ANativeWindow *window = nullptr; // ANativeWindow 用来渲染画面的 == Surface对象

    //设置 GazeApplied callback
    void setGazeAppliedCallback(JNIEnv* env, jobject callback_calib);

    bool start_application(int mode);                  // 启动应用记录
    bool stop_application();                           // 结束应用记录

    /* 设置屏幕虚化的参数:  radius: 半径，sigma: 模糊程度，blur_mode: 虚化模式，blur_ch: 虚化通道 */
    bool set_blur_params_func(float radius, float sigma, int blur_mode, int blur_ch);
    /* 设置注视点 x: 横坐标[0~1]，y:纵坐标[0~1] */
    bool set_stare_point_func(float x, float y);
    /* 设置扫视的目标点 x: 横坐标[0~1]，y:纵坐标[0~1] */
    bool set_glance_point_func(float x, float y);

    bool collect_gaze(bool valid, float x, float y, float dist, float duration);        // 实时传入眼动数据，返回自动结束眼动标志
    string get_record_data_func(int mode);                // 读取应用期间记录的数据，以json的格式返回
};

#endif //MIT_DD_DEMO_GAZEAPPLICATION_H
