#include "Blur.h"

PqBlur::PqBlur(float macular_radius){

    // 当设定的值在 0.5 ~ 5.5 之间才有效
    if(0.5 <= macular_radius && macular_radius <= 5.5)
    {
        macular_blur_radius = macular_radius;
    }

    // gaussian blur of surface
    blur_enable_flag = false;
    pq_blr = nullptr;
    channel = default_channel;    // default to red
    mode = default_mode;          // default to InnerBlur
    GKSigma = default_inner_GKSigma;
    pq_radius = default_pq_radius;
}

PqBlur::~PqBlur() {
    // 当释放跟踪服务时，关闭虚化效果
    blur_enable_flag = false;
    if (pq_blr != nullptr) { // 添加了对classPtr是否为空的检查
        LOGI("------- PqBlur::~PqBlur: pq_blr != nullptr deletePQ_BLR!!!! ------");
        deletePQ_BLR(pq_blr);
        pq_blr = nullptr;
    }
}

void PqBlur::draw_gaze_result_func(float x, float y, float dist)
{
    // open gaussian switch
    if(blur_enable_flag && pq_blr != nullptr)
    {
        // 距离范围限定35~65, 中心点限定屏幕范围内
        if(x > 0 && x < 1.0  && y > 0 && y < 1.0)
        {
            int screen_x = (int)(x * visual_image_width);
            int screen_y = (int)(y * visual_image_height);
            LOGI("------- PqBlur draw_gaze_result_func pq_blr: x = %f, y = %f,  dist = %f------", x, y, dist);
            if(dist >= 35 && dist <= 65)
            {
                int circle_radius = (int)(dist / 11.33F / 19.5F * 1080.0F / 3.0F * macular_blur_radius);  // 根据距离计算虚化面积
                pq_blr->SetRadius(circle_radius)->SetPos(screen_x, screen_y)->Done();
            }
            else
            {
                pq_blr->SetPos(screen_x, screen_y)->Done();
            }
        }
    }
    else
    {
        LOGE("------- PqBlur::draw_gaze_result_func: blur_enable_flag is false, can not blur region!!!! ------");
    }
}

// 视线区域虚化的开关设置
void PqBlur::set_red_blur_enable(bool valid)
{
    if (valid)
    {
        // open gaussian switch
        if(!blur_enable_flag)
        {
            blur_enable_flag = true;
            pq_blr = newPQ_BLR();
            pq_blr->SetRadius(pq_radius)->SetChannel(channel)->SetPos((int)(visual_image_width / 2), (int)(visual_image_height / 2))->Done();
            switch (mode) {
                case InnerBlur:
                    pq_blr->SetGaussKer(InnerGKSize, InnerGKSize, GKSigma)->SetMode(mode)->Done();
                    break;
                case OuterBlur:
                    pq_blr->SetGaussKer(OuterGKSize, OuterGKSize, default_outer_GKSigma)->SetMode(mode)->Done();
                    break;
                default:
                    pq_blr->SetMode(mode)->Done();
            }
        }
        else
        {
            LOGI("------- PqBlur::set_red_blur_enable: blur_enable_flag is already true, do not repeat the opening operation!!!! ------");
        }
    }
    else
    {   // 关闭视线区域虚化的功能
        blur_enable_flag = false;
        if (pq_blr != nullptr) { // 添加了对classPtr是否为空的检查
            LOGI("------- PqBlur::set_red_blur_enable: pq_blr != nullptr deletePQ_BLR!!!! ------");
            deletePQ_BLR(pq_blr);
            pq_blr = nullptr;
        }
    }
}

// 视线区域黄斑区的遮盖半径
bool PqBlur::set_red_blur_radius(float macular_radius)
{
    bool flag = false;
    LOGI("------- GazeTracker set_red_blur_radius pq_blr: macular_radius = %f ------", macular_radius);
    // 当设定的值在 0.5 ~ 5.5 之间才有效
    if(0.5 <= macular_radius && macular_radius <= 5.5)
    {
        macular_blur_radius = macular_radius;
        if(pq_blr != nullptr)
        {
            int circle_radius = (int)(50.0F / 11.33F / 19.5F * 1080.0F / 3.0F * macular_blur_radius);  // 根据距离计算虚化面积
            pq_blr->SetRadius(circle_radius)->Done();
            flag = true;
        }
    }
    else
    {
        LOGE("------- PqBlur::set_red_blur_radius: macular_radius only support 0.5 ~ 5.5, input is %f !!!! ------", macular_radius);
    }
    return flag;
}

// 视线区域的模糊程度
bool PqBlur::set_red_blur_sigma(float blur_sigma)
{
    bool flag = false;
    if (mode == 0)
    {
        // 当设定的值在1 ~ 150才有效
        if(1.0 <= blur_sigma && blur_sigma <= 150.0)
        {
            GKSigma = blur_sigma / 10.0F;
            if(pq_blr != nullptr)
            {
                pq_blr->SetGaussKer(InnerGKSize, InnerGKSize, GKSigma)->Done();
                flag = true;
            }
        }
        else
        {
            LOGE("------- PqBlur::set_red_blur_sigma: macular_radius only support 5.0 ~ 50.0, input is %f !!!! ------", blur_sigma);
        }
    }
    else
    {
        LOGI("------- PqBlur::set_red_blur_sigma: When the blur mode is InnerBlur, the sigma value can be set!!!");
    }
    return flag;
}

// 屏幕虚化模式设置
bool PqBlur::set_red_blur_mode(int blur_mode)
{
    bool flag = false;
    // 支持3种模式
    if(0 <= blur_mode && blur_mode <= 3)
    {
        mode = blur_mode;
        if(pq_blr != nullptr)
        {
            switch (mode) {
                case InnerBlur:
                    pq_blr->SetGaussKer(InnerGKSize, InnerGKSize, GKSigma)->SetMode(mode)->Done();
                    break;
                case OuterBlur:
                    pq_blr->SetGaussKer(OuterGKSize, OuterGKSize, default_outer_GKSigma)->SetMode(mode)->Done();
                    break;
                default:
                    pq_blr->SetMode(mode)->Done();
            }
            flag = true;
        }
    }
    else
    {
        LOGE("------- PqBlur::set_red_blur_mode: blur_mode only support 0～3, input is %d !!!! ------", blur_mode);
    }
    return flag;
}

// 屏幕虚化通道对象设置
bool PqBlur::set_red_blur_channel(int blur_ch)
{
    bool flag = false;
    // 支持7种组合模式，二进制控制
    if(1 <= blur_ch && blur_ch <= 7)
    {
        channel = blur_ch;
        if(pq_blr != nullptr)
        {
            pq_blr->SetChannel(channel)->Done();
            flag = true;
        }
    }
    else
    {
        LOGE("------- PqBlur::set_red_blur_channel: blur_ch only support 1～7, input is %d !!!! ------", blur_ch);
    }
    return flag;
}
