//
// Created by user on 2024/12/16.
//

#ifndef MIT_DD_DEMO_POSEALIGN_H
#define MIT_DD_DEMO_POSEALIGN_H
#include "utils.h"

using namespace cv;
using namespace std;
using namespace chrono;

typedef std::chrono::high_resolution_clock Clock;

/**
 * 姿势校准
 */
class PoseAlign {

public:
    PoseAlign();
    ~PoseAlign();

    // 记录判断姿势是否在限定范围内的时间
    chrono::time_point<steady_clock, nanoseconds> record_time;
private:
    pose_pupil_points pose_preview_eye_points;                  // 记录上一帧的眼睛位置
    pose_pupil_points setting_refer_points;                     // 记录参考的眼睛位置
    float expect_interpupillary_distance;                       // 计算距离50cm的时候的瞳距的像素值


    // time record
    int remaining;                   // 【姿势校准】倒计时，剩余时间
    int cunt_down_second;            // 【姿势校准】设置时长
    // 记录判断姿势是否在限定范围内的时间
    chrono::time_point<steady_clock, nanoseconds> pose_align_time;    // 记录姿势对准的持续时间

    bool finish_flag;                // 是否完成姿势校准
    bool ignoring;                   // 强制关闭校准页面后，间隔一定的时间再次触发

public:
    void start_aligning_head_pose();      // 开启姿势校准&初始化
    void stop_aligning_head_pose();       // 关闭姿势校准
    /**
     * 跟踪过程中的姿势评估函数
     * @param det_result
     * @param isTracking true 表示眼动追踪过程中调用 false 表示校准过程调用
     * @return
     */
    bool recognize_pose_valid(const frame_detection_result& det_result,bool isTracking);
    pose_align_result head_pose_aligning(const frame_detection_result& det_result);   // 姿势校准过程函数

    Mat draw_poseAlign_func(cv::Mat &bgr_img, const pose_align_result& pose_result);
};
#endif //MIT_DD_DEMO_POSEALIGN_H
