<?xml version="1.0" encoding="UTF-8"?>
<svg width="2000" height="2100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #e74c3c; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .flow-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 3; }
      .method-box { fill: #fff3cd; stroke: #f39c12; stroke-width: 2; }
      .view-box { fill: #e3f2fd; stroke: #3498db; stroke-width: 2; }
      .native-box { fill: #f3e5f5; stroke: #9b59b6; stroke-width: 2; }
      .practice-box { fill: #ffeaa7; stroke: #fdcb6e; stroke-width: 2; }
      .flow-arrow { stroke: #27ae60; stroke-width: 4; fill: none; marker-end: url(#greenarrow); }
      .detail-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#bluearrow); }
    </style>
    <marker id="greenarrow" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#27ae60" />
    </marker>
    <marker id="bluearrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="1000" y="35" text-anchor="middle" class="title">🔧 updateUI完整实现机制：从数据到View的全流程</text>
  
  <!-- 主数据流 -->
  <rect x="50" y="60" width="1900" height="100" rx="15" class="flow-box"/>
  <text x="1000" y="85" text-anchor="middle" class="subtitle">📊 完整数据流: Repository → ViewModel → LiveData → Observer → updateUI() → ViewBinding → View操作 → 界面渲染</text>

  <!-- 流程箭头 -->
  <line x1="130" y1="120" x2="210" y2="120" class="flow-arrow"/>
  <line x1="290" y1="120" x2="370" y2="120" class="flow-arrow"/>
  <line x1="450" y1="120" x2="530" y2="120" class="flow-arrow"/>
  <line x1="610" y1="120" x2="690" y2="120" class="flow-arrow"/>
  <line x1="770" y1="120" x2="850" y2="120" class="flow-arrow"/>
  <line x1="930" y1="120" x2="1010" y2="120" class="flow-arrow"/>
  <line x1="1090" y1="120" x2="1170" y2="120" class="flow-arrow"/>
  <line x1="1250" y1="120" x2="1330" y2="120" class="flow-arrow"/>
  <line x1="1410" y1="120" x2="1490" y2="120" class="flow-arrow"/>
  <line x1="1570" y1="120" x2="1650" y2="120" class="flow-arrow"/>
  <line x1="1730" y1="120" x2="1810" y2="120" class="flow-arrow"/>

  <text x="80" y="115" class="text">🌐 网络</text>
  <text x="250" y="115" class="text">📦 Repository</text>
  <text x="410" y="115" class="text">🧠 ViewModel</text>
  <text x="570" y="115" class="text">📡 LiveData</text>
  <text x="730" y="115" class="text">👀 Observer</text>
  <text x="890" y="115" class="text">🔧 updateUI()</text>
  <text x="1050" y="115" class="text">🔗 ViewBinding</text>
  <text x="1210" y="115" class="text">🎨 View操作</text>
  <text x="1370" y="115" class="text">🖼️ 重绘</text>
  <text x="1530" y="115" class="text">👁️ 用户</text>
  <text x="1690" y="115" class="text">✨ 完成</text>
  <text x="1850" y="115" class="text">🎯 反馈</text>

  <!-- ViewBinding机制详解 -->
  <rect x="50" y="180" width="950" height="200" rx="15" class="view-box"/>
  <text x="525" y="205" text-anchor="middle" class="subtitle">🔗 ViewBinding 机制详解</text>

  <text x="60" y="230" class="text">🏗️ <tspan class="subtitle">1. 编译时生成:</tspan></text>
  <text x="60" y="250" class="code">// build.gradle.kts 中启用</text>
  <text x="60" y="270" class="code">buildFeatures { viewBinding = true }</text>
  <text x="60" y="290" class="code">// 自动生成 ActivityMainBinding 类</text>

  <text x="60" y="315" class="text">🔧 <tspan class="subtitle">2. 初始化绑定:</tspan></text>
  <text x="60" y="335" class="code">private lateinit var binding: ActivityMainBinding</text>
  <text x="60" y="355" class="code">binding = ActivityMainBinding.inflate(layoutInflater)</text>
  <text x="60" y="375" class="code">setContentView(binding.root)  // 设置根视图</text>

  <text x="520" y="230" class="text">🎯 <tspan class="subtitle">3. 类型安全访问:</tspan></text>
  <text x="520" y="250" class="code">// 直接访问View，无需findViewById</text>
  <text x="520" y="270" class="code">binding.tvCopyright.text = "版权信息"</text>
  <text x="520" y="290" class="code">binding.ivLogo.setImageResource(R.drawable.logo)</text>
  <text x="520" y="310" class="code">binding.clSetting.setOnClickListener { ... }</text>

  <text x="520" y="335" class="text">✅ <tspan class="subtitle">4. 优势特点:</tspan></text>
  <text x="520" y="355" class="code">• 编译时类型检查，避免ClassCastException</text>
  <text x="520" y="375" class="code">• 空安全，避免NullPointerException</text>

  <!-- updateUserInfo 完整实现 -->
  <rect x="50" y="400" width="950" height="320" rx="15" class="method-box"/>
  <text x="525" y="425" text-anchor="middle" class="subtitle">👤 updateUserInfo() + ViewBinding 完整实现流程</text>

  <text x="60" y="450" class="text">🔍 <tspan class="subtitle">1. 方法调用触发:</tspan></text>
  <text x="60" y="470" class="code">userVM.userLiveData.observe(this) { user -></text>
  <text x="60" y="490" class="code">    updateUserInfo(user)  // Observer回调触发</text>
  <text x="60" y="510" class="code">}</text>

  <text x="60" y="535" class="text">📊 <tspan class="subtitle">2. 数据处理逻辑:</tspan></text>
  <text x="60" y="555" class="code">private fun updateUserInfo(user: User?) {</text>
  <text x="60" y="575" class="code">    val userGender = UserManager.getUserGender(user)</text>
  <text x="60" y="595" class="code">    // 业务逻辑处理，确定显示内容</text>

  <text x="60" y="620" class="text">🖼️ <tspan class="subtitle">3. ImageView操作(传统方式):</tspan></text>
  <text x="60" y="640" class="code">    when(userGender) {</text>
  <text x="60" y="660" class="code">        Gender.FEMALE -> ImageLoader.loadImageWithPlaceholder(</text>
  <text x="60" y="680" class="code">            this, user?.avatar, R.drawable.ic_female_avatar_round, ivUserAvatar)</text>
  <text x="60" y="700" class="code">    }</text>
  
  <text x="520" y="450" class="text">📝 <tspan class="subtitle">4. TextView操作(传统方式):</tspan></text>
  <text x="520" y="470" class="code">    // 用户名显示控制</text>
  <text x="520" y="490" class="code">    tvUserName.isVisible = user?.username.orEmpty().isNotBlank()</text>
  <text x="520" y="510" class="code">    tvUserName.text = user?.username</text>

  <text x="520" y="535" class="text">🎨 <tspan class="subtitle">5. 条件显示逻辑:</tspan></text>
  <text x="520" y="555" class="code">    // 性别信息处理</text>
  <text x="520" y="575" class="code">    tvUserGender.isVisible = userGender != null</text>
  <text x="520" y="595" class="code">    tvUserGender.text = getString(R.string.str_gender_, when(userGender) {</text>
  <text x="520" y="615" class="code">        Gender.MALE -> getString(R.string.str_gender_male)</text>
  <text x="520" y="635" class="code">        Gender.FEMALE -> getString(R.string.str_gender_female)</text>
  <text x="520" y="655" class="code">        else -> getString(R.string.str_confidential)</text>
  <text x="520" y="675" class="code">    })</text>

  <text x="520" y="700" class="text">🔧 <tspan class="subtitle">6. 其他业务逻辑:</tspan></text>
  <text x="520" y="720" class="code">    user?.let { playPromptVoice(it) }  // 语音播报</text>

  <!-- updateDeviceInfo + ViewBinding 完整实现 -->
  <rect x="1020" y="180" width="930" height="540" rx="15" class="method-box"/>
  <text x="1485" y="205" text-anchor="middle" class="subtitle">🔧 updateDeviceInfo() + ViewBinding 完整实现流程</text>

  <text x="1030" y="230" class="text">🔍 <tspan class="subtitle">1. 方法调用触发:</tspan></text>
  <text x="1030" y="250" class="code">deviceVM.deviceInfoLiveData.observe(this) { deviceInfo -></text>
  <text x="1030" y="270" class="code">    updateDeviceInfo(deviceInfo)  // Observer回调触发</text>
  <text x="1030" y="290" class="code">}</text>

  <text x="1030" y="315" class="text">🖼️ <tspan class="subtitle">2. ViewBinding图片更新:</tspan></text>
  <text x="1030" y="335" class="code">private fun updateDeviceInfo(deviceInfo: DeviceInfo?) {</text>
  <text x="1030" y="355" class="code">    // 使用ViewBinding访问ImageView</text>
  <text x="1030" y="375" class="code">    ImageLoader.loadImageWithPlaceholder(this,</text>
  <text x="1030" y="395" class="code">        deviceInfo?.logo, 0, R.drawable.ic_main_logo, binding.ivLogo)</text>

  <text x="1030" y="420" class="text">📝 <tspan class="subtitle">3. ViewBinding文本更新:</tspan></text>
  <text x="1030" y="440" class="code">    // 类型安全的TextView访问</text>
  <text x="1030" y="460" class="code">    binding.tvCopyright.text = deviceInfo?.copyright</text>

  <text x="1030" y="485" class="text">🎯 <tspan class="subtitle">4. 界面状态切换:</tspan></text>
  <text x="1030" y="505" class="code">    if (DeviceManager.isAvailable(deviceInfo)) {</text>
  <text x="1030" y="525" class="code">        when(DeviceManager.getStartupMode(deviceInfo)) {</text>
  <text x="1030" y="545" class="code">            QRCODE_WECHAT -> showScanCode()  // Fragment切换</text>
  <text x="1030" y="565" class="code">            ACCESS_CODE -> showDetectionCode()</text>
  <text x="1030" y="585" class="code">            MANUAL_ENTRY -> showInputInfo()</text>
  <text x="1030" y="605" class="code">        }</text>
  <text x="1030" y="625" class="code">    }</text>
  <text x="1030" y="645" class="code">}</text>

  <text x="1500" y="230" class="text">🎨 <tspan class="subtitle">5. ViewBinding背景更新:</tspan></text>
  <text x="1500" y="250" class="code">private fun showScanCode() {</text>
  <text x="1500" y="270" class="code">    // 使用ViewBinding设置背景</text>
  <text x="1500" y="290" class="code">    binding.clMainRoot.setBackgroundResource(</text>
  <text x="1500" y="310" class="code">        R.drawable.ic_main_bg_1)</text>
  <text x="1500" y="330" class="code">}</text>

  <text x="1500" y="355" class="text">📱 <tspan class="subtitle">6. Fragment管理:</tspan></text>
  <text x="1500" y="375" class="code">    val fragment = supportFragmentManager</text>
  <text x="1500" y="395" class="code">        .findFragmentByTag(FRAGMENT_TAG)</text>
  <text x="1500" y="415" class="code">    if (fragment == null) {</text>
  <text x="1500" y="435" class="code">        supportFragmentManager.beginTransaction()</text>
  <text x="1500" y="455" class="code">            .replace(R.id.fl_start_up_mode, newFragment)</text>
  <text x="1500" y="475" class="code">            .commitAllowingStateLoss()</text>
  <text x="1500" y="495" class="code">    }</text>

  <text x="1500" y="520" class="text">🔗 <tspan class="subtitle">7. ViewBinding优势体现:</tspan></text>
  <text x="1500" y="540" class="code">• binding.ivLogo 编译时类型检查</text>
  <text x="1500" y="560" class="code">• binding.tvCopyright 空安全保证</text>
  <text x="1500" y="580" class="code">• binding.clMainRoot 直接访问，无需findViewById</text>
  <text x="1500" y="600" class="code">• 性能优化：避免运行时View查找</text>
  <text x="1500" y="620" class="code">• 代码简洁：减少样板代码</text>

  <text x="1030" y="680" class="small-text">💡 ViewBinding vs 传统方式对比：findViewById(R.id.tv_copyright) → binding.tvCopyright</text>
  <text x="1030" y="700" class="small-text">🎯 ViewBinding自动生成，编译时验证，运行时高效，是现代Android开发的标准做法</text>

  <!-- DataBinding vs ViewBinding 对比 -->
  <rect x="50" y="740" width="1900" height="160" rx="15" class="native-box"/>
  <text x="1000" y="765" text-anchor="middle" class="subtitle">🔗 DataBinding vs ViewBinding 对比分析</text>

  <text x="60" y="790" class="text">📊 <tspan class="subtitle">ViewBinding (当前项目使用):</tspan></text>
  <text x="60" y="810" class="code">• 编译时生成类型安全的绑定类</text>
  <text x="60" y="830" class="code">• 只处理View引用，不处理数据绑定</text>
  <text x="60" y="850" class="code">• 性能开销小，编译速度快</text>
  <text x="60" y="870" class="code">• 使用方式: binding.textView.text = data</text>

  <text x="500" y="790" class="text">🎯 <tspan class="subtitle">DataBinding (更强大但复杂):</tspan></text>
  <text x="500" y="810" class="code">• 支持布局文件中的数据绑定表达式</text>
  <text x="500" y="830" class="code">• 双向绑定、Observable字段、BindingAdapter</text>
  <text x="500" y="850" class="code">• 布局中直接写: android:text="@{user.name}"</text>
  <text x="500" y="870" class="code">• 自动更新UI，减少手动updateUI调用</text>

  <text x="1000" y="790" class="text">⚖️ <tspan class="subtitle">选择建议:</tspan></text>
  <text x="1000" y="810" class="code">• 简单项目：ViewBinding足够</text>
  <text x="1000" y="830" class="code">• 复杂UI：DataBinding更合适</text>
  <text x="1000" y="850" class="code">• 当前项目：ViewBinding + 手动updateUI</text>
  <text x="1000" y="870" class="code">• 平衡了简洁性和功能性</text>

  <text x="1500" y="790" class="text">🔄 <tspan class="subtitle">升级路径:</tspan></text>
  <text x="1500" y="810" class="code">• ViewBinding → DataBinding 渐进升级</text>
  <text x="1500" y="830" class="code">• 保持现有updateUI逻辑</text>
  <text x="1500" y="850" class="code">• 逐步引入Observable字段</text>
  <text x="1500" y="870" class="code">• 最终实现声明式UI更新</text>

  <!-- View操作的底层实现 -->
  <rect x="50" y="920" width="1900" height="280" rx="15" class="view-box"/>
  <text x="1000" y="945" text-anchor="middle" class="subtitle">🎨 ViewBinding + View操作的底层实现机制</text>
  
  <!-- ViewBinding + TextView实现 -->
  <text x="60" y="970" class="text">📝 <tspan class="subtitle">ViewBinding + TextView.text 实现:</tspan></text>
  <text x="60" y="990" class="code">1. binding.textView.text = "新内容"</text>
  <text x="60" y="1010" class="code">2. ViewBinding提供类型安全的TextView引用</text>
  <text x="60" y="1030" class="code">3. TextView.setText() 调用</text>
  <text x="60" y="1050" class="code">4. 检查文本是否变化</text>
  <text x="60" y="1070" class="code">5. 调用 invalidate() 标记需要重绘</text>
  <text x="60" y="1090" class="code">6. 触发 onDraw() 重新绘制文本</text>
  <text x="60" y="1110" class="code">7. 用户看到新文本内容</text>
  
  <!-- ViewBinding + ImageView实现 -->
  <text x="400" y="970" class="text">🖼️ <tspan class="subtitle">ViewBinding + ImageView 实现:</tspan></text>
  <text x="400" y="990" class="code">1. binding.imageView.setImageResource(R.drawable.icon)</text>
  <text x="400" y="1010" class="code">2. ViewBinding确保ImageView引用非空</text>
  <text x="400" y="1030" class="code">3. 从Resources加载Drawable</text>
  <text x="400" y="1050" class="code">4. 设置到ImageView的mDrawable字段</text>
  <text x="400" y="1070" class="code">5. 调用 invalidate() 标记重绘</text>
  <text x="400" y="1090" class="code">6. onDraw() 中绘制新图片</text>
  <text x="400" y="1110" class="code">7. 界面显示新图片</text>
  
  <!-- ViewBinding + 可见性实现 -->
  <text x="740" y="970" class="text">👁️ <tspan class="subtitle">ViewBinding + View.isVisible 实现:</tspan></text>
  <text x="740" y="990" class="code">1. binding.view.isVisible = false</text>
  <text x="740" y="1010" class="code">2. ViewBinding确保View引用有效</text>
  <text x="740" y="1030" class="code">3. 设置 visibility = View.GONE</text>
  <text x="740" y="1050" class="code">4. 父布局重新测量(measure)</text>
  <text x="740" y="1070" class="code">5. 重新布局(layout)，不为该View分配空间</text>
  <text x="740" y="1090" class="code">6. 重绘(draw)，跳过该View</text>
  <text x="740" y="1110" class="code">7. View从界面消失</text>

  <!-- ViewBinding + 布局参数实现 -->
  <text x="1080" y="970" class="text">📐 <tspan class="subtitle">ViewBinding + LayoutParams 实现:</tspan></text>
  <text x="1080" y="990" class="code">1. val params = binding.view.layoutParams</text>
  <text x="1080" y="1010" class="code">2. params.width = newWidth</text>
  <text x="1080" y="1030" class="code">3. binding.view.layoutParams = params</text>
  <text x="1080" y="1050" class="code">4. 调用 requestLayout()</text>
  <text x="1080" y="1070" class="code">5. 触发父View的 onMeasure()</text>
  <text x="1080" y="1090" class="code">6. 触发 onLayout() 重新定位</text>
  <text x="1080" y="1110" class="code">7. View以新尺寸显示</text>

  <!-- ViewBinding + 动画实现 -->
  <text x="1420" y="970" class="text">🎬 <tspan class="subtitle">ViewBinding + ObjectAnimator 实现:</tspan></text>
  <text x="1420" y="990" class="code">1. ObjectAnimator.ofFloat(binding.view, "rotation", 0f, 360f)</text>
  <text x="1420" y="1010" class="code">2. ViewBinding提供安全的View引用</text>
  <text x="1420" y="1030" class="code">3. 每帧调用 setRotation(animatedValue)</text>
  <text x="1420" y="1050" class="code">4. 更新View的transformation matrix</text>
  <text x="1420" y="1070" class="code">5. 调用 invalidate() 重绘</text>
  <text x="1420" y="1090" class="code">6. 硬件加速绘制旋转效果</text>
  <text x="1420" y="1110" class="code">7. 用户看到旋转动画</text>

  <!-- ViewBinding + 悬浮窗实现 -->
  <text x="1760" y="970" class="text">🪟 <tspan class="subtitle">ViewBinding + WindowManager 实现:</tspan></text>
  <text x="1760" y="990" class="code">1. windowManager.updateViewLayout(binding.dotView, params)</text>
  <text x="1760" y="1010" class="code">2. ViewBinding确保dotView引用正确</text>
  <text x="1760" y="1030" class="code">3. 调用系统WindowManagerService</text>
  <text x="1760" y="1050" class="code">4. 更新Surface位置参数</text>
  <text x="1760" y="1070" class="code">5. SurfaceFlinger重新合成</text>
  <text x="1760" y="1090" class="code">6. 硬件层面更新显示</text>
  <text x="1760" y="1110" class="code">7. 悬浮窗移动到新位置</text>

  <!-- Native层实现 -->
  <rect x="50" y="1220" width="1900" height="200" rx="15" class="native-box"/>
  <text x="1000" y="1245" text-anchor="middle" class="subtitle">🔧 Native层View操作实现</text>

  <text x="60" y="1270" class="text">🎨 <tspan class="subtitle">ANativeWindow 直接绘制流程:</tspan></text>
  <text x="60" y="1290" class="code">1. ANativeWindow_setBuffersGeometry(window, width, height, format)</text>
  <text x="60" y="1310" class="code">2. ANativeWindow_lock(window, &buffer, nullptr)  // 锁定缓冲区</text>
  <text x="60" y="1330" class="code">3. memcpy(dstData, srcData, dataSize)  // 直接拷贝像素数据</text>
  <text x="60" y="1350" class="code">4. ANativeWindow_unlockAndPost(window)  // 解锁并提交显示</text>
  <text x="60" y="1370" class="code">5. SurfaceFlinger合成到屏幕  // 系统级渲染</text>
  <text x="60" y="1390" class="code">6. 用户看到实时图像  // 硬件加速显示</text>

  <text x="1000" y="1270" class="text">👁️ <tspan class="subtitle">眼动追踪视点更新流程:</tspan></text>
  <text x="1000" y="1290" class="code">1. 眼动算法计算出视点坐标 (x, y)</text>
  <text x="1000" y="1310" class="code">2. 坐标转换: screenX = x * screenWidth</text>
  <text x="1000" y="1330" class="code">3. dotParams.x = screenX; dotParams.y = screenY</text>
  <text x="1000" y="1350" class="code">4. windowManager.updateViewLayout(dotView, dotParams)</text>
  <text x="1000" y="1370" class="code">5. 系统更新悬浮窗位置</text>
  <text x="1000" y="1390" class="code">6. 用户看到视点实时跟随眼球移动</text>

  <!-- 线程安全和最佳实践 -->
  <rect x="50" y="1440" width="1900" height="320" rx="15" class="practice-box"/>
  <text x="1000" y="1465" text-anchor="middle" class="subtitle">🛡️ ViewBinding + 线程安全与最佳实践</text>

  <text x="60" y="1490" class="text">🧵 <tspan class="subtitle">ViewBinding + 线程切换:</tspan></text>
  <text x="60" y="1510" class="code">// 子线程中获取数据</text>
  <text x="60" y="1530" class="code">Thread {</text>
  <text x="60" y="1550" class="code">    val data = heavyNetworkCall()</text>
  <text x="60" y="1570" class="code">    runOnUiThread {</text>
  <text x="60" y="1590" class="code">        binding.textView.text = data  // ViewBinding安全更新</text>
  <text x="60" y="1610" class="code">    }</text>
  <text x="60" y="1630" class="code">}.start()</text>
  
  <text x="400" y="1490" class="text">🔄 <tspan class="subtitle">ViewBinding + 协程方式:</tspan></text>
  <text x="400" y="1510" class="code">lifecycleScope.launch(Dispatchers.IO) {</text>
  <text x="400" y="1530" class="code">    val data = repository.getData()</text>
  <text x="400" y="1550" class="code">    withContext(Dispatchers.Main) {</text>
  <text x="400" y="1570" class="code">        binding.textView.text = data  // ViewBinding安全更新</text>
  <text x="400" y="1590" class="code">        updateUserInfo(data)  // 主线程更新</text>
  <text x="400" y="1610" class="code">    }</text>
  <text x="400" y="1630" class="code">}</text>

  <text x="740" y="1490" class="text">📡 <tspan class="subtitle">ViewBinding + LiveData方式:</tspan></text>
  <text x="740" y="1510" class="code">// ViewModel中</text>
  <text x="740" y="1530" class="code">viewModelScope.launch {</text>
  <text x="740" y="1550" class="code">    val result = repository.getData()</text>
  <text x="740" y="1570" class="code">    userLiveData.postValue(result)  // 任意线程</text>
  <text x="740" y="1590" class="code">}</text>
  <text x="740" y="1610" class="code">// Activity中自动在主线程回调</text>
  <text x="740" y="1630" class="code">userLiveData.observe(this) { updateUserInfo(it) }</text>
  <text x="740" y="1650" class="code">// updateUserInfo中使用ViewBinding更新UI</text>

  <text x="1200" y="1490" class="text">⚡ <tspan class="subtitle">ViewBinding + 性能优化:</tspan></text>
  <text x="1200" y="1510" class="code">// 批量更新，减少重绘次数</text>
  <text x="1200" y="1530" class="code">binding.rootView.visibility = View.INVISIBLE  // 暂时隐藏</text>
  <text x="1200" y="1550" class="code">binding.textView1.text = data1</text>
  <text x="1200" y="1570" class="code">binding.textView2.text = data2</text>
  <text x="1200" y="1590" class="code">binding.imageView.setImageResource(resId)</text>
  <text x="1200" y="1610" class="code">binding.rootView.visibility = View.VISIBLE  // 一次性显示</text>

  <text x="1600" y="1490" class="text">🛡️ <tspan class="subtitle">ViewBinding + 错误处理:</tspan></text>
  <text x="1600" y="1510" class="code">try {</text>
  <text x="1600" y="1530" class="code">    binding.textView.text = data</text>
  <text x="1600" y="1550" class="code">    windowManager.updateViewLayout(binding.dotView, params)</text>
  <text x="1600" y="1570" class="code">} catch (e: Exception) {</text>
  <text x="1600" y="1590" class="code">    Logger.e(TAG, "ViewBinding update failed: ${e.message}")</text>
  <text x="1600" y="1610" class="code">    // ViewBinding确保View引用安全</text>
  <text x="1600" y="1630" class="code">    recreateBinding()</text>
  <text x="1600" y="1650" class="code">}</text>

  <text x="60" y="1675" class="text">💡 <tspan class="subtitle">ViewBinding关键要点:</tspan></text>
  <text x="60" y="1695" class="small-text">• ViewBinding提供编译时类型安全，避免findViewById的性能开销和ClassCastException</text>
  <text x="60" y="1715" class="small-text">• 结合LiveData自动处理线程切换和生命周期，是最安全的UI更新方式</text>
  <text x="60" y="1735" class="small-text">• binding对象确保View引用非空，避免NullPointerException，提高代码健壮性</text>

  <!-- 完整流程总结 -->
  <rect x="50" y="1780" width="1900" height="260" rx="15" class="flow-box"/>
  <text x="1000" y="1805" text-anchor="middle" class="subtitle">🎯 ViewBinding + updateUI 完整流程总结</text>

  <text x="60" y="1830" class="text">📊 <tspan class="subtitle">典型更新流程(含ViewBinding):</tspan></text>
  <text x="60" y="1850" class="small-text">1. 用户操作/定时任务 → 2. Repository网络请求 → 3. ViewModel处理业务逻辑 → 4. LiveData.postValue()发布数据</text>
  <text x="60" y="1870" class="small-text">5. Observer.onChanged()回调 → 6. updateXXX()方法调用 → 7. ViewBinding提供类型安全View引用 → 8. 具体View属性设置</text>
  <text x="60" y="1890" class="small-text">9. View.invalidate()标记重绘 → 10. ViewRootImpl调度重绘 → 11. View.onDraw()执行 → 12. 硬件加速渲染 → 13. 用户看到界面变化</text>

  <text x="60" y="1915" class="text">🔧 <tspan class="subtitle">ViewBinding实际应用案例:</tspan></text>
  <text x="60" y="1935" class="small-text">• 用户登录 → 获取用户信息 → updateUserInfo() → binding.ivUserAvatar头像加载 + binding.tvUserName文本设置 → 用户信息界面更新</text>
  <text x="60" y="1955" class="small-text">• 设备状态变化 → 获取设备配置 → updateDeviceInfo() → binding.clMainRoot背景切换 + binding.ivLogo更新 → 主界面状态切换</text>
  <text x="60" y="1975" class="small-text">• 眼动追踪 → 实时坐标计算 → Native层处理 → WindowManager更新binding.dotView → 悬浮视点实时跟随</text>

  <text x="60" y="2000" class="text">🎯 <tspan class="subtitle">ViewBinding核心价值:</tspan></text>
  <text x="60" y="2020" class="small-text">• 编译时类型检查，避免运行时崩溃 • 空安全保证，减少NullPointerException • 性能优化，避免findViewById查找开销 • 代码简洁，提高开发效率</text>

</svg>
