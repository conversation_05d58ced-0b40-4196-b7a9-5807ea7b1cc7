package com.airdoc.mpd.ppg.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: FrequencyDomainParameters
 * Author by lilin,Date on 2025/6/20 16:22
 * PS: Not easy to write code, please indicate.
 * 频域参数数据类
 */
@Parcelize
data class FrequencyDomainParameters(
    // 总功率(0.0-0.4 Hz)
    val totalPower: Double,
    // 极低频功率 (0.003-0.04 Hz)
    val vlf: Double,
    // 低频功率 (0.04-0.15 Hz)
    val lf: Double,
    // 高频功率 (0.15-0.4 Hz)
    val hf: Double,
    // LF/HF比值
    val lfHfRatio: Double,
    //分段功率(0.0-0.4 Hz step 未0.01)
    val stepPower: List<Double>
): Parcelable
