<svg width="1400" height="1800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font: bold 24px Arial; fill: #2c3e50; }
      .subtitle { font: bold 18px Arial; fill: #34495e; }
      .section-title { font: bold 16px Arial; fill: #e74c3c; }
      .text { font: 14px Arial; fill: #2c3e50; }
      .code { font: 12px 'Courier New', monospace; fill: #27ae60; }
      .small-text { font: 11px Arial; fill: #7f8c8d; }
      .binding-box { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .livedata-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .manual-box { fill: #fef9e7; stroke: #f39c12; stroke-width: 2; }
      .activity-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .fragment-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .dialog-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; }
      .item-box { fill: #e0f2f1; stroke: #009688; stroke-width: 2; }
    </style>
  </defs>

  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">MPD项目 ViewBinding架构与UI更新模式分析</text>
  
  <!-- ViewBinding类分类展示 -->
  <text x="50" y="70" class="section-title">📱 项目中生产的ViewBinding类 (23个)</text>
  
  <!-- Activity Binding类 -->
  <rect x="50" y="90" width="320" height="280" rx="10" class="activity-box"/>
  <text x="210" y="115" text-anchor="middle" class="subtitle">🏠 Activity Binding (10个)</text>
  <text x="60" y="140" class="text">• ActivityMainBinding</text>
  <text x="60" y="160" class="text">• ActivityDetectionBinding</text>
  <text x="60" y="180" class="text">• ActivityDetection1Binding</text>
  <text x="60" y="200" class="text">• ActivityDetectionWebBinding</text>
  <text x="60" y="220" class="text">• ActivityHrvBinding</text>
  <text x="60" y="240" class="text">• ActivityCalibrationBinding</text>
  <text x="60" y="260" class="text">• ActivityConfigBinding</text>
  <text x="60" y="280" class="text">• ActivityMoreSettingsBinding</text>
  <text x="60" y="300" class="text">• ActivityScanBinding</text>
  <text x="60" y="320" class="text">• ActivityUpdateBinding</text>
  <text x="60" y="345" class="small-text">💡 用于Activity的根视图绑定</text>

  <!-- Fragment Binding类 -->
  <rect x="390" y="90" width="320" height="280" rx="10" class="fragment-box"/>
  <text x="550" y="115" text-anchor="middle" class="subtitle">🧩 Fragment Binding (4个)</text>
  <text x="400" y="140" class="text">• FragmentDetectionCodeDetectionBinding</text>
  <text x="400" y="160" class="text">• FragmentDeviceExceptionBinding</text>
  <text x="400" y="180" class="text">• FragmentInputInfoDetectionBinding</text>
  <text x="400" y="200" class="text">• FragmentScanCodeDetectionBinding</text>
  <text x="400" y="240" class="small-text">💡 用于Fragment的视图绑定</text>
  <text x="400" y="260" class="small-text">🔗 与Activity共享ViewModel数据</text>

  <!-- Dialog Binding类 -->
  <rect x="730" y="90" width="320" height="280" rx="10" class="dialog-box"/>
  <text x="890" y="115" text-anchor="middle" class="subtitle">💬 Dialog Binding (6个)</text>
  <text x="740" y="140" class="text">• DialogCommonLoadingBinding</text>
  <text x="740" y="160" class="text">• DialogDeviceInfoBinding</text>
  <text x="740" y="180" class="text">• DialogLanguageSettingsBinding</text>
  <text x="740" y="200" class="text">• DialogSelectionAgeBinding</text>
  <text x="740" y="220" class="text">• DialogStartupModeSettingsBinding</text>
  <text x="740" y="240" class="text">• DialogUpdateBinding</text>
  <text x="740" y="280" class="small-text">💡 用于对话框的视图绑定</text>
  <text x="740" y="300" class="small-text">⚡ 轻量级UI组件</text>

  <!-- Item和Layout Binding类 -->
  <rect x="1070" y="90" width="280" height="280" rx="10" class="item-box"/>
  <text x="1210" y="115" text-anchor="middle" class="subtitle">📋 Item/Layout Binding (3个)</text>
  <text x="1080" y="140" class="text">• ItemDetectionBinding</text>
  <text x="1080" y="160" class="text">• ItemLanguageSettingsBinding</text>
  <text x="1080" y="180" class="text">• LayoutMenuPopupWindowBinding</text>
  <text x="1080" y="220" class="small-text">💡 用于RecyclerView Item</text>
  <text x="1080" y="240" class="small-text">🎨 和自定义布局组件</text>

  <!-- ViewBinding使用模式 -->
  <text x="50" y="420" class="section-title">🔧 ViewBinding使用模式</text>
  
  <rect x="50" y="440" width="650" height="200" rx="10" class="binding-box"/>
  <text x="375" y="465" text-anchor="middle" class="subtitle">ViewBinding标准使用流程</text>
  
  <text x="60" y="490" class="text">1️⃣ 声明绑定变量:</text>
  <text x="60" y="510" class="code">private lateinit var binding: ActivityMainBinding</text>
  
  <text x="60" y="535" class="text">2️⃣ 在onCreate中初始化:</text>
  <text x="60" y="555" class="code">binding = ActivityMainBinding.inflate(layoutInflater)</text>
  <text x="60" y="575" class="code">setContentView(binding.root)</text>
  
  <text x="60" y="600" class="text">3️⃣ 类型安全访问View:</text>
  <text x="60" y="620" class="code">binding.tvCopyright.text = "版权信息"  // 无需findViewById</text>

  <!-- LiveData使用模式 -->
  <text x="50" y="680" class="section-title">📡 LiveData + ViewBinding使用模式</text>
  
  <rect x="50" y="700" width="650" height="280" rx="10" class="livedata-box"/>
  <text x="375" y="725" text-anchor="middle" class="subtitle">MVVM架构 - 数据驱动UI更新</text>
  
  <text x="60" y="750" class="text">🏗️ ViewModel中定义LiveData:</text>
  <text x="60" y="770" class="code">val userLiveData = MutableLiveData&lt;User?&gt;()</text>
  <text x="60" y="790" class="code">val deviceInfoLiveData = MutableLiveData&lt;DeviceInfo?&gt;()</text>
  
  <text x="60" y="815" class="text">📊 ViewModel中更新数据:</text>
  <text x="60" y="835" class="code">viewModelScope.launch {</text>
  <text x="60" y="855" class="code">    val result = repository.getUserInfo(token)</text>
  <text x="60" y="875" class="code">    userLiveData.postValue(result)  // 线程安全</text>
  <text x="60" y="895" class="code">}</text>
  
  <text x="60" y="920" class="text">👀 Activity/Fragment中观察数据:</text>
  <text x="60" y="940" class="code">userVM.userLiveData.observe(this) { user -></text>
  <text x="60" y="960" class="code">    updateUserInfo(user)  // 自动在主线程回调</text>
  <text x="60" y="980" class="code">}</text>

  <!-- 手动UI更新模式 -->
  <text x="750" y="680" class="section-title">✋ 手动UI更新模式</text>
  
  <rect x="750" y="700" width="600" height="280" rx="10" class="manual-box"/>
  <text x="1050" y="725" text-anchor="middle" class="subtitle">直接操作View - 即时响应</text>
  
  <text x="760" y="750" class="text">📝 文本更新:</text>
  <text x="760" y="770" class="code">binding.tvUserName.text = user.name</text>
  <text x="760" y="790" class="code">binding.tvDetectionCode.setText("")  // 清空输入</text>
  
  <text x="760" y="815" class="text">🖼️ 图片更新:</text>
  <text x="760" y="835" class="code">binding.ivLogo.setImageResource(R.drawable.logo)</text>
  <text x="760" y="855" class="code">ImageLoader.loadImageWithPlaceholder(..., binding.ivUserAvatar)</text>
  
  <text x="760" y="880" class="text">👁️ 可见性控制:</text>
  <text x="760" y="900" class="code">binding.llLoading.isVisible = isLoading</text>
  <text x="760" y="920" class="code">binding.ivException.setImageResource(R.drawable.ic_error)</text>
  
  <text x="760" y="945" class="text">🎯 事件监听:</text>
  <text x="760" y="965" class="code">binding.tvCancelDetection.setOnSingleClickListener { finish() }</text>

  <!-- 实际使用案例 -->
  <text x="50" y="1020" class="section-title">💼 项目中的实际使用案例</text>

  <!-- MainActivity案例 -->
  <rect x="50" y="1040" width="420" height="200" rx="10" class="activity-box"/>
  <text x="260" y="1065" text-anchor="middle" class="subtitle">MainActivity - 设备信息更新</text>
  <text x="60" y="1090" class="text">🔧 ViewBinding初始化:</text>
  <text x="60" y="1110" class="code">binding = ActivityMainBinding.inflate(layoutInflater)</text>
  <text x="60" y="1130" class="text">📡 LiveData观察:</text>
  <text x="60" y="1150" class="code">deviceVM.deviceInfoLiveData.observe(this) {</text>
  <text x="60" y="1170" class="code">    updateDeviceInfo(it)</text>
  <text x="60" y="1190" class="code">}</text>
  <text x="60" y="1210" class="text">✋ 手动UI更新:</text>
  <text x="60" y="1230" class="code">binding.tvCopyright.text = deviceInfo?.copyright</text>

  <!-- DetectionActivity案例 -->
  <rect x="490" y="1040" width="420" height="200" rx="10" class="activity-box"/>
  <text x="700" y="1065" text-anchor="middle" class="subtitle">DetectionActivity - 用户信息展示</text>
  <text x="500" y="1090" class="text">📡 多个LiveData观察:</text>
  <text x="500" y="1110" class="code">userVM.userLiveData.observe(this) { updateUserInfo(it) }</text>
  <text x="500" y="1130" class="code">userVM.detectionProjectsLiveData.observe(this) {</text>
  <text x="500" y="1150" class="code">    adapter.setDetectionProjects(it?.projects ?: emptyList())</text>
  <text x="500" y="1170" class="code">}</text>
  <text x="500" y="1190" class="text">🖼️ 图片加载:</text>
  <text x="500" y="1210" class="code">ImageLoader.loadImageWithPlaceholder(..., ivUserAvatar)</text>
  <text x="500" y="1230" class="code">binding.tvUserName.text = user?.name</text>

  <!-- Fragment案例 -->
  <rect x="930" y="1040" width="420" height="200" rx="10" class="fragment-box"/>
  <text x="1140" y="1065" text-anchor="middle" class="subtitle">DeviceExceptionFragment - 异常处理</text>
  <text x="940" y="1090" class="text">📡 LiveData + 协程:</text>
  <text x="940" y="1110" class="code">deviceVM.deviceInfoLiveData.observe(this) {</text>
  <text x="940" y="1130" class="code">    lifecycleScope.launch(Dispatchers.Default) {</text>
  <text x="940" y="1150" class="code">        val networkState = NetworkManager.obtainRealTimeNetworkState()</text>
  <text x="940" y="1170" class="code">        withContext(Dispatchers.Main) {</text>
  <text x="940" y="1190" class="code">            ivException.setImageResource(R.drawable.ic_error)</text>
  <text x="940" y="1210" class="code">            tvException.text = it?.disableMsg</text>
  <text x="940" y="1230" class="code">        }</text>

  <!-- 架构优势对比 -->
  <text x="50" y="1280" class="section-title">⚖️ 三种UI更新方式对比</text>

  <rect x="50" y="1300" width="450" height="180" rx="10" class="livedata-box"/>
  <text x="275" y="1325" text-anchor="middle" class="subtitle">📡 LiveData + ViewBinding (推荐)</text>
  <text x="60" y="1350" class="text">✅ 生命周期安全，自动取消订阅</text>
  <text x="60" y="1370" class="text">✅ 数据驱动，UI与业务逻辑解耦</text>
  <text x="60" y="1390" class="text">✅ 线程安全，自动切换到主线程</text>
  <text x="60" y="1410" class="text">✅ 适用于复杂数据流和状态管理</text>
  <text x="60" y="1430" class="text">🎯 使用场景：用户信息、设备状态、网络数据</text>
  <text x="60" y="1450" class="small-text">💡 项目中主要用于MainActivity、DetectionActivity</text>

  <rect x="520" y="1300" width="450" height="180" rx="10" class="manual-box"/>
  <text x="745" y="1325" text-anchor="middle" class="subtitle">✋ 手动UI更新</text>
  <text x="530" y="1350" class="text">⚡ 响应速度快，直接操作View</text>
  <text x="530" y="1370" class="text">🎯 适用于简单的UI状态切换</text>
  <text x="530" y="1390" class="text">⚠️ 需要手动管理生命周期</text>
  <text x="530" y="1410" class="text">⚠️ 容易造成内存泄漏</text>
  <text x="530" y="1430" class="text">🎯 使用场景：按钮点击、输入框清空、加载状态</text>
  <text x="530" y="1450" class="small-text">💡 项目中用于DetectionCodeDetectionFragment</text>

  <rect x="990" y="1300" width="360" height="180" rx="10" class="binding-box"/>
  <text x="1170" y="1325" text-anchor="middle" class="subtitle">🔧 ViewBinding优势</text>
  <text x="1000" y="1350" class="text">✅ 编译时类型安全</text>
  <text x="1000" y="1370" class="text">✅ 无需findViewById</text>
  <text x="1000" y="1390" class="text">✅ 自动生成，减少样板代码</text>
  <text x="1000" y="1410" class="text">✅ 支持空安全</text>
  <text x="1000" y="1430" class="text">🚀 性能优于反射查找</text>
  <text x="1000" y="1450" class="small-text">💡 项目中全面采用</text>

  <!-- 最佳实践建议 -->
  <text x="50" y="1520" class="section-title">🏆 最佳实践建议</text>

  <rect x="50" y="1540" width="1300" height="120" rx="10" class="binding-box"/>
  <text x="700" y="1565" text-anchor="middle" class="subtitle">MPD项目UI更新最佳实践</text>

  <text x="60" y="1590" class="text">1️⃣ <tspan class="code">优先使用LiveData + ViewBinding</tspan> 处理数据驱动的UI更新（用户信息、设备状态、网络数据）</text>
  <text x="60" y="1610" class="text">2️⃣ <tspan class="code">手动UI更新</tspan> 用于简单的交互响应（按钮点击、输入框操作、即时状态切换）</text>
  <text x="60" y="1630" class="text">3️⃣ <tspan class="code">在Fragment中共享Activity的ViewModel</tspan> 保持数据一致性</text>
  <text x="60" y="1650" class="text">4️⃣ <tspan class="code">使用协程处理异步操作</tspan> 结合LiveData实现线程安全的UI更新</text>

</svg>
