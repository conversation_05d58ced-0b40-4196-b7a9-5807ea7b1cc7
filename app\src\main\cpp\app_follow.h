//
// Created by user on 2025/1/2.
//

#ifndef MIT_DD_DEMO_APP_FOLLOW_H
#define MIT_DD_DEMO_APP_FOLLOW_H

#include "utils.h"

using namespace cv;
using namespace std;

class GazeFollow {
public:
    GazeFollow();
    ~GazeFollow();

public:
    std::vector<cv::Point3f> gaze_data_list;    // 应用期间采集的眼动数据
    cv::Mat gaussian_mask;                      // 对注视结果进行后处理修正的高斯掩模

public:
    void reset_params_func();                                                   // 清空缓存数据
    void collect_data(float x, float y, float duration);                        // 应用期间传输数据
    std::vector<std::vector<Point3f>> postprocess_trajectory_data();            // 处理眼动轨迹的数据
    string get_record_jsondata();                                               // 获取json的数据
};

#endif //MIT_DD_DEMO_APP_FOLLOW_H
