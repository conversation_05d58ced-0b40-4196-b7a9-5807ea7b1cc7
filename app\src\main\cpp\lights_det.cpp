#include "lights_det.h"

lightsDetect::lightsDetect()
{
    // ----- init rknn input buff -----
    data_inputs = (rknn_input*)malloc(sizeof(rknn_input));
    memset(&(data_inputs[0]), 0, sizeof(rknn_input));
    data_inputs[0].index = 0;
    data_inputs[0].pass_through = 0;
    data_inputs[0].type = RKNN_TENSOR_FLOAT32;
    data_inputs[0].fmt = RKNN_TENSOR_NHWC;
    data_inputs[0].size = lights_det_net_w * lights_det_net_h * get_type_size(RKNN_TENSOR_FLOAT32);

    // ---- init rknn output buff ----
    det_outputs = (rknn_output*)malloc(sizeof(rknn_output));
    memset(&(det_outputs[0]), 0, sizeof(rknn_output));
    det_outputs[0].want_float = 1;

    // ---- init rknn output buff ----
    seg_outputs = (rknn_output*)malloc(sizeof(rknn_output));
    memset(&(seg_outputs[0]), 0, sizeof(rknn_output));
    seg_outputs[0].want_float = 1;
}
lightsDetect::~lightsDetect()
{
    rknn_destroy(light_model_ctx);
    rknn_destroy(pupil_model_ctx);
    free(data_inputs);
    free(det_outputs);
    free(seg_outputs);
}

void lightsDetect::load_model(char* det_model_path, char* seg_model_path, bool& model_isready)
{
    // load light detection model
    int det_ret = rknn_init(&light_model_ctx, (void *) det_model_path, 0, 0, NULL);
    if (det_ret < 0) {
        printf("light det rknn init fail! ret=%d\n", det_ret);
        model_isready = false;
    } else {
        printf("light det rknn init succeed! ret=%d\n", det_ret);
        model_isready = true;
    }
    // load pupil segmentation model
    int seg_ret = rknn_init(&pupil_model_ctx, (void *) seg_model_path, 0, 0, NULL);
    if (seg_ret < 0) {
        printf("seg model rknn init fail! ret=%d\n", seg_ret);
        model_isready = false;
    } else {
        printf("seg model rknn init succeed! ret=%d\n", seg_ret);
        model_isready = true;
    }
}


void lightsDetect::recognize(const cv::Mat& src, cv::Rect& iris_rect, std::vector<cv::Point2f> & lights_point_list, cv::Point2f & pupil_cent_point)
{
    int iris_rect_x0 = iris_rect.x;
    int iris_rect_y0 = iris_rect.y;

    Mat patch = src(iris_rect);

    int img_w = patch.cols;
    int img_h = patch.rows;
    float ratio_w = (float)img_w / (float)lights_det_net_w;
    float ratio_h = (float)img_h / (float)lights_det_net_h;

    cv::Mat rs_image;
    cv::resize(patch, rs_image, cv::Size(lights_det_net_w, lights_det_net_h));

    // 创建一个目标Mat对象用于存放转换后的数据，类型为CV_32FC1，即单通道float32
    cv::Mat blob_image;
    // 使用convertTo()函数进行类型转换
    rs_image.convertTo(blob_image, CV_32FC1); // 第二个参数是目标类型
    cout << "light det blob_image size = : " << blob_image.size<< endl;

    // --- input set ---
    auto *resize_buf = (unsigned char *)malloc(lights_det_net_h* lights_det_net_w * get_type_size(RKNN_TENSOR_FLOAT32));
    size_t dataSize = blob_image.total() * sizeof(float);
    memcpy(resize_buf, blob_image.data, dataSize);
    data_inputs[0].buf = resize_buf;

    int det_ret = 0;
    det_ret = rknn_inputs_set(light_model_ctx, 1, data_inputs);
    check_ret(det_ret, "light det rknn inputs set");

    int seg_ret = 0;
    seg_ret = rknn_inputs_set(pupil_model_ctx, 1, data_inputs);
    check_ret(seg_ret, "pupil seg rknn inputs set");

    // --- deploy ---
    det_ret = rknn_run(light_model_ctx, NULL);
    check_ret(det_ret, "light det rknn run");
    seg_ret = rknn_run(pupil_model_ctx, NULL);
    check_ret(seg_ret, "pupil seg rknn run");

    // -------- Get the inference output result --------
    det_ret = rknn_outputs_get(light_model_ctx, 1, det_outputs, NULL);
    // 获取buff的指针，用于直接访问内存
    auto* det_buff_ptr = (float*)det_outputs[0].buf;
    check_ret(det_ret, "light det rknn outputs get");
    seg_ret = rknn_outputs_get(pupil_model_ctx, 1, seg_outputs, NULL);
    // 获取buff的指针，用于直接访问内存
    auto* seg_buff_ptr = (float*)seg_outputs[0].buf;
    check_ret(seg_ret, "pupil seg rknn outputs get");

    // --- iris segmentation result postprocessing ---
    cv::Mat pupil_mask_prob(lights_det_net_h, lights_det_net_w, CV_32F, seg_buff_ptr);
    cv::Mat iris_mask_prob(lights_det_net_h, lights_det_net_w, CV_32F, seg_buff_ptr + lights_det_net_h * lights_det_net_w);
    cv::Mat pupil_mask_prob_rs, iris_mask_prob_rs;
    cv::resize(pupil_mask_prob, pupil_mask_prob_rs, cv::Size(img_w, img_h), 0, 0, INTER_LINEAR);
    cv::resize(iris_mask_prob, iris_mask_prob_rs, cv::Size(img_w, img_h), 0, 0, INTER_LINEAR);

    cv::Mat seg_pupil_mask = cv::Mat::zeros(img_h, img_w, CV_8UC1);
    cv::Mat seg_iris_mask = cv::Mat::zeros(img_h, img_w, CV_8UC1);
//    cv::threshold(pupil_mask_prob_rs, seg_pupil_mask, pupil_seg_mask_threshold, 1, cv::THRESH_BINARY);
//    cv::threshold(iris_mask_prob_rs, seg_iris_mask, iris_seg_mask_threshold, 1, cv::THRESH_BINARY);
    manual_threahold_float(pupil_mask_prob_rs, seg_pupil_mask, pupil_seg_mask_threshold, 1);
    manual_threahold_float(iris_mask_prob_rs, seg_iris_mask, iris_seg_mask_threshold, 1);

    // --- light detection result postprocessing ---
    cv::Mat left_mask(lights_det_net_h, lights_det_net_w, CV_32F, det_buff_ptr);
    cv::Mat right_mask(lights_det_net_h, lights_det_net_w, CV_32F, det_buff_ptr + lights_det_net_h * lights_det_net_w);

    cv::Mat left_mask_rs, right_mask_rs;
    cv::resize(left_mask, left_mask_rs, cv::Size(img_w, img_h), 0, 0, INTER_LINEAR);
    cv::resize(right_mask, right_mask_rs, cv::Size(img_w, img_h), 0, 0, INTER_LINEAR);

    cv::Mat norm_left_mask = cv::Mat::zeros(img_h, img_w, left_mask_rs.type());
    cv::Mat norm_right_mask = cv::Mat::zeros(img_h, img_w, left_mask_rs.type());

    // multiply light det mask and iris seg mask
    for (int i = 0; i < img_h; i++)
    {
        const float *left_mask_ptr = left_mask_rs.ptr<float>(i);
        const float *right_mask_ptr = right_mask_rs.ptr<float>(i);
        const uchar *seg_mask_ptr = seg_iris_mask.ptr<uchar>(i);

        auto *norm_left_mask_ptr = norm_left_mask.ptr<float>(i);
        auto *norm_right_mask_ptr = norm_right_mask.ptr<float>(i);

        for (int j = 0; j < img_w; j++)
        {
            norm_left_mask_ptr[j] = left_mask_ptr[j] * (float)seg_mask_ptr[j];
            norm_right_mask_ptr[j] = right_mask_ptr[j] * (float)seg_mask_ptr[j];
        }
    }
    /*
    seg_iris_mask.convertTo(seg_iris_mask, left_mask_rs.type());
    norm_left_mask = left_mask_rs.mul(seg_iris_mask);
    norm_right_mask = right_mask_rs.mul(seg_iris_mask);

    // det result
    cv::Point left_point_cent = cv::Point(-1, -1);
    cv::Point right_point_cent = cv::Point(-1, -1);
    // get detection result
    double left_minVal, left_maxVal;
    int left_maxp[2], left_minp[2];
    minMaxIdx(norm_left_mask, &left_minVal, &left_maxVal, left_minp, left_maxp);
     cout << "max of norm_left_mask is " << left_maxVal << endl;
    if (left_maxVal > det_points_prob_threshold)
    {
        cv::Point ori_left_light_point;
        ori_left_light_point.x = left_maxp[1] + iris_rect_x0;
        ori_left_light_point.y = left_maxp[0] + iris_rect_y0;
        refine_lights_pixel(src, ori_left_light_point, left_point_cent);
    }
    double right_minVal, right_maxVal;
    int right_maxp[2], right_minp[2];
    minMaxIdx(norm_right_mask, &right_minVal, &right_maxVal, right_minp, right_maxp);
     cout << "max of norm_right_mask is " << right_maxVal << endl;
    if(right_maxVal > det_points_prob_threshold)
    {
        cv::Point ori_right_light_point;
        ori_right_light_point.x = right_maxp[1] + iris_rect_x0;
        ori_right_light_point.y = right_maxp[0] + iris_rect_y0;
        refine_lights_pixel(src, ori_right_light_point, right_point_cent);
    }
     */
    // get detection result
    cv::Point ori_left_light_point = cv::Point(-1, -1);
    cv::Point ori_right_light_point = cv::Point(-1, -1);
    cv::Point2f left_point_cent = cv::Point2f(-1.0F, -1.0F);
    cv::Point2f right_point_cent = cv::Point2f(-1.0F, -1.0F);
    float left_maxVal = 0.0;
    float right_maxVal = 0.0;
    find_best_point_by_prob_mask(norm_left_mask, ori_left_light_point, left_maxVal, img_w, img_h, det_points_prob_threshold);
    find_best_point_by_prob_mask(norm_right_mask, ori_right_light_point, right_maxVal, img_w, img_h, det_points_prob_threshold);
    if (ori_left_light_point.x > 0 && ori_left_light_point.y > 0 && left_maxVal >= det_points_prob_threshold)
    {
        ori_left_light_point.x += iris_rect_x0;
        ori_left_light_point.y += iris_rect_y0;
        refine_lights_pixel(src, ori_left_light_point, left_point_cent);
    }
    if(ori_right_light_point.x > 0 && ori_right_light_point.y > 0 && right_maxVal >= det_points_prob_threshold)
    {
        ori_right_light_point.x += iris_rect_x0;
        ori_right_light_point.y += iris_rect_y0;
        refine_lights_pixel(src, ori_right_light_point, right_point_cent);
    }

    /*
    cv::Moments m = moments(seg_pupil_mask, true);
    double pupil_cent_x = -1;
    double pupil_cent_y = -1;
    if (m.m00 > 0 && m.m01 > 0 && m.m10 > 0)
    {
        pupil_cent_x = m.m10 / m.m00 + iris_rect_x0;
        pupil_cent_y = m.m01 / m.m00 + iris_rect_y0;
    }
     pupil_cent_point = Point((int)pupil_cent_x, (int)pupil_cent_y);
     */
    cv::Point2f pupil_cent_p_ = cv::Point2f(-1.0F, -1.0F);
    bool not_blink = findForegroundCenter(seg_pupil_mask, pupil_cent_p_);
    if (not_blink)
    {
        pupil_cent_point = cv::Point2f(pupil_cent_p_.x + (float)iris_rect_x0, pupil_cent_p_.y + (float)iris_rect_y0);
    }
    else
    {
        //如果判断为眨眼
        pupil_cent_point = cv::Point2f(-1.0F, -1.0F);
    }


    // two light point distance
    if(right_point_cent.x > 0 && left_point_cent.x > 0)
    {
        if(right_point_cent.x - left_point_cent.x > det_points_dist_threshold)
        {
            lights_point_list[0] = left_point_cent;
            lights_point_list[1] = right_point_cent;
        }
        else
        {
            if(right_maxVal > left_maxVal)
            {
                left_point_cent = cv::Point2f(-1.0F, -1.0F);
            }
            else
            {
                right_point_cent = cv::Point2f(-1.0F, -1.0F);
            }
            lights_point_list[0] = left_point_cent;
            lights_point_list[1] = right_point_cent;
        }
    }
    else
    {
        lights_point_list[0] = left_point_cent;
        lights_point_list[1] = right_point_cent;
    }

    // release input and output buffer
    det_ret = rknn_outputs_release(light_model_ctx, 1, det_outputs);
    check_ret(det_ret, "light det rknn outputs release");
    seg_ret = rknn_outputs_release(pupil_model_ctx, 1, seg_outputs);
    check_ret(seg_ret, "pupil seg rknn outputs release");
    delete[] resize_buf;
}