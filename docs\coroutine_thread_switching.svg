<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #1a365d; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2d3748; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2d3748; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #4a5568; }
      .thread-main { fill: #667eea; stroke: #5a67d8; stroke-width: 2; }
      .thread-io { fill: #4fd1c7; stroke: #38b2ac; stroke-width: 2; }
      .thread-default { fill: #68d391; stroke: #48bb78; stroke-width: 2; }
      .lifecycle { fill: #f6ad55; stroke: #ed8936; stroke-width: 2; }
      .arrow { stroke: #2d3748; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .switch-arrow { stroke: #667eea; stroke-width: 3; fill: none; marker-end: url(#switchhead); }
      .dashed { stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="switchhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#e74c3c" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">Android 协程线程切换详细策略图</text>
  
  <!-- 生命周期管理区域 -->
  <rect x="50" y="60" width="300" height="180" class="lifecycle" rx="10" opacity="0.3"/>
  <text x="200" y="80" text-anchor="middle" class="subtitle">生命周期管理</text>
  
  <!-- Activity/Service 生命周期 -->
  <rect x="70" y="100" width="120" height="40" fill="#fff" stroke="#f39c12" stroke-width="2" rx="5"/>
  <text x="130" y="125" text-anchor="middle" class="text">Activity/Service</text>
  
  <rect x="210" y="100" width="120" height="40" fill="#fff" stroke="#f39c12" stroke-width="2" rx="5"/>
  <text x="270" y="125" text-anchor="middle" class="text">ViewModel</text>
  
  <!-- 协程作用域 -->
  <rect x="70" y="160" width="120" height="40" fill="#fff" stroke="#f39c12" stroke-width="2" rx="5"/>
  <text x="130" y="180" text-anchor="middle" class="text">lifecycleScope</text>
  <text x="130" y="195" text-anchor="middle" class="small-text">MainScope()</text>
  
  <rect x="210" y="160" width="120" height="40" fill="#fff" stroke="#f39c12" stroke-width="2" rx="5"/>
  <text x="270" y="180" text-anchor="middle" class="text">viewModelScope</text>
  <text x="270" y="195" text-anchor="middle" class="small-text">自动管理</text>
  
  <!-- 连接线 -->
  <line x1="130" y1="140" x2="130" y2="160" class="arrow"/>
  <line x1="270" y1="140" x2="270" y2="160" class="arrow"/>

  <!-- 线程池区域 -->
  <rect x="400" y="60" width="750" height="180" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="775" y="80" text-anchor="middle" class="subtitle">协程调度器 (Dispatchers)</text>
  
  <!-- Main 线程 -->
  <rect x="420" y="100" width="200" height="60" class="thread-main" rx="8"/>
  <text x="520" y="125" text-anchor="middle" class="text" fill="white">Dispatchers.Main</text>
  <text x="520" y="145" text-anchor="middle" class="small-text" fill="white">UI更新、回调、LiveData</text>
  
  <!-- IO 线程池 -->
  <rect x="650" y="100" width="200" height="60" class="thread-io" rx="8"/>
  <text x="750" y="125" text-anchor="middle" class="text" fill="white">Dispatchers.IO</text>
  <text x="750" y="145" text-anchor="middle" class="small-text" fill="white">网络、文件、数据库</text>
  
  <!-- Default 线程池 -->
  <rect x="880" y="100" width="200" height="60" class="thread-default" rx="8"/>
  <text x="980" y="125" text-anchor="middle" class="text" fill="white">Dispatchers.Default</text>
  <text x="980" y="145" text-anchor="middle" class="small-text" fill="white">CPU密集型、计算</text>
  
  <!-- 具体使用场景 -->
  <text x="600" y="280" text-anchor="middle" class="subtitle">具体使用场景与切换策略</text>
  
  <!-- 场景1: 图像处理 -->
  <rect x="50" y="310" width="350" height="120" fill="#fff" stroke="#3498db" stroke-width="2" rx="8"/>
  <text x="225" y="330" text-anchor="middle" class="subtitle">图像处理 (TrackingManager)</text>
  
  <rect x="70" y="350" width="100" height="30" class="thread-io" rx="5"/>
  <text x="120" y="370" text-anchor="middle" class="small-text" fill="white">gazeTracking()</text>
  
  <rect x="280" y="350" width="100" height="30" class="thread-main" rx="5"/>
  <text x="330" y="370" text-anchor="middle" class="small-text" fill="white">UI回调</text>
  
  <path d="M 170 365 Q 225 340 280 365" class="switch-arrow"/>
  <text x="225" y="355" text-anchor="middle" class="small-text">withContext</text>
  
  <text x="70" y="400" class="small-text">launch(Dispatchers.IO)</text>
  <text x="70" y="415" class="small-text">→ withContext(Main)</text>

  <!-- 场景2: 网络请求 -->
  <rect x="420" y="310" width="350" height="120" fill="#fff" stroke="#68d391" stroke-width="2" rx="8"/>
  <text x="595" y="330" text-anchor="middle" class="subtitle">网络请求 (Repository)</text>
  
  <rect x="440" y="350" width="100" height="30" class="thread-io" rx="5"/>
  <text x="490" y="370" text-anchor="middle" class="small-text" fill="white">API调用</text>
  
  <rect x="650" y="350" width="100" height="30" class="thread-main" rx="5"/>
  <text x="700" y="370" text-anchor="middle" class="small-text" fill="white">更新LiveData</text>
  
  <path d="M 540 365 Q 595 340 650 365" class="switch-arrow"/>
  <text x="595" y="355" text-anchor="middle" class="small-text">自动切换</text>
  
  <text x="440" y="400" class="small-text">viewModelScope.launch</text>
  <text x="440" y="415" class="small-text">→ suspend fun</text>

  <!-- 场景3: 定时任务 -->
  <rect x="800" y="310" width="350" height="120" fill="#fff" stroke="#667eea" stroke-width="2" rx="8"/>
  <text x="975" y="330" text-anchor="middle" class="subtitle">定时任务 (GazeTrackService)</text>
  
  <rect x="820" y="350" width="100" height="30" class="thread-default" rx="5"/>
  <text x="870" y="370" text-anchor="middle" class="small-text" fill="white">delay循环</text>
  
  <rect x="1030" y="350" width="100" height="30" class="thread-main" rx="5"/>
  <text x="1080" y="370" text-anchor="middle" class="small-text" fill="white">更新进度</text>
  
  <path d="M 920 365 Q 975 340 1030 365" class="switch-arrow"/>
  <text x="975" y="355" text-anchor="middle" class="small-text">withContext</text>
  
  <text x="820" y="400" class="small-text">launch(Dispatchers.Default)</text>
  <text x="820" y="415" class="small-text">→ withContext(Main)</text>

  <!-- 生命周期管理策略 -->
  <text x="600" y="480" text-anchor="middle" class="subtitle">生命周期管理策略</text>
  
  <!-- 自动管理 -->
  <rect x="50" y="500" width="350" height="100" fill="#e6fffa" stroke="#48bb78" stroke-width="2" rx="8"/>
  <text x="225" y="520" text-anchor="middle" class="subtitle">自动管理</text>
  <text x="70" y="540" class="text">• lifecycleScope: 组件销毁时自动取消</text>
  <text x="70" y="555" class="text">• viewModelScope: onCleared时自动取消</text>
  <text x="70" y="570" class="text">• 子协程随父协程自动取消</text>
  <text x="70" y="585" class="text">• 异常传播和处理</text>

  <!-- 手动管理 -->
  <rect x="420" y="500" width="350" height="100" fill="#fffaf0" stroke="#ed8936" stroke-width="2" rx="8"/>
  <text x="595" y="520" text-anchor="middle" class="subtitle">手动管理</text>
  <text x="440" y="540" class="text">• Job.cancel(): 手动取消协程</text>
  <text x="440" y="555" class="text">• stopXXX(): 业务层停止操作</text>
  <text x="440" y="570" class="text">• invokeOnCompletion: 完成回调</text>
  <text x="440" y="585" class="text">• 资源清理: native、网络、文件</text>

  <!-- 异常处理 -->
  <rect x="800" y="500" width="350" height="100" fill="#fed7d7" stroke="#667eea" stroke-width="2" rx="8"/>
  <text x="975" y="520" text-anchor="middle" class="subtitle">异常处理</text>
  <text x="820" y="540" class="text">• try-catch包装防止崩溃</text>
  <text x="820" y="555" class="text">• 资源释放保证</text>
  <text x="820" y="570" class="text">• 错误日志记录</text>
  <text x="820" y="585" class="text">• 优雅降级处理</text>

  <!-- 切换原则 -->
  <text x="600" y="640" text-anchor="middle" class="subtitle">线程切换原则</text>
  
  <rect x="100" y="660" width="200" height="80" fill="#e6fffa" stroke="#48bb78" stroke-width="2" rx="5"/>
  <text x="200" y="680" text-anchor="middle" class="text" fill="#48bb78">需要切换</text>
  <text x="120" y="700" class="small-text">• 耗时操作 → UI更新</text>
  <text x="120" y="715" class="small-text">• 网络/文件 → 主线程</text>
  <text x="120" y="730" class="small-text">• 算法计算 → 结果显示</text>

  <rect x="350" y="660" width="200" height="80" fill="#fffaf0" stroke="#ed8936" stroke-width="2" rx="5"/>
  <text x="450" y="680" text-anchor="middle" class="text" fill="#ed8936">可选切换</text>
  <text x="370" y="700" class="small-text">• 轻量级计算</text>
  <text x="370" y="715" class="small-text">• 内存操作</text>
  <text x="370" y="730" class="small-text">• 简单数据处理</text>

  <rect x="600" y="660" width="200" height="80" fill="#edf2f7" stroke="#667eea" stroke-width="2" rx="5"/>
  <text x="700" y="680" text-anchor="middle" class="text" fill="#667eea">不需要切换</text>
  <text x="620" y="700" class="small-text">• 纯UI操作</text>
  <text x="620" y="715" class="small-text">• suspend函数内部已处理</text>
  <text x="620" y="730" class="small-text">• 主线程快速操作</text>

  <!-- 核心原则 -->
  <rect x="850" y="660" width="300" height="80" fill="#e0f2fe" stroke="#4fd1c7" stroke-width="2" rx="5"/>
  <text x="1000" y="680" text-anchor="middle" class="text" fill="#4fd1c7">核心原则</text>
  <text x="870" y="700" class="small-text">保持主线程流畅</text>
  <text x="870" y="715" class="small-text">耗时操作放后台线程</text>
  <text x="870" y="730" class="small-text">合理的资源管理</text>

  <!-- 协程详细实现区域 -->
  <text x="800" y="780" text-anchor="middle" class="subtitle">协程具体实现细节</text>

  <!-- 协程构建器详解 -->
  <rect x="50" y="800" width="480" height="160" fill="#f7fafc" stroke="#a0aec0" stroke-width="2" rx="8"/>
  <text x="290" y="820" text-anchor="middle" class="subtitle">协程构建器 (Coroutine Builders)</text>

  <!-- launch -->
  <rect x="70" y="840" width="140" height="50" fill="#667eea" stroke="#5a67d8" stroke-width="2" rx="5"/>
  <text x="140" y="860" text-anchor="middle" class="text" fill="white">launch</text>
  <text x="140" y="875" text-anchor="middle" class="small-text" fill="white">启动协程，不返回值</text>

  <!-- async -->
  <rect x="230" y="840" width="140" height="50" fill="#68d391" stroke="#48bb78" stroke-width="2" rx="5"/>
  <text x="300" y="860" text-anchor="middle" class="text" fill="white">async</text>
  <text x="300" y="875" text-anchor="middle" class="small-text" fill="white">并发执行，返回Deferred</text>

  <!-- runBlocking -->
  <rect x="390" y="840" width="140" height="50" fill="#fc8181" stroke="#e53e3e" stroke-width="2" rx="5"/>
  <text x="460" y="860" text-anchor="middle" class="text" fill="white">runBlocking</text>
  <text x="460" y="875" text-anchor="middle" class="small-text" fill="white">阻塞当前线程</text>

  <!-- 代码示例 -->
  <text x="70" y="910" class="small-text">launch { /* 火忘式 */ }</text>
  <text x="70" y="925" class="small-text">launch(Dispatchers.IO) { }</text>
  <text x="70" y="940" class="small-text">lifecycleScope.launch { }</text>

  <text x="230" y="910" class="small-text">val deferred = async { }</text>
  <text x="230" y="925" class="small-text">val result = deferred.await()</text>
  <text x="230" y="940" class="small-text">并发处理多个任务</text>

  <text x="390" y="910" class="small-text">runBlocking { }</text>
  <text x="390" y="925" class="small-text">测试中使用</text>
  <text x="390" y="940" class="small-text">main函数中使用</text>

  <!-- 协程上下文详解 -->
  <rect x="550" y="800" width="480" height="160" fill="#f7fafc" stroke="#a0aec0" stroke-width="2" rx="8"/>
  <text x="790" y="820" text-anchor="middle" class="subtitle">协程上下文 (CoroutineContext)</text>

  <!-- Job -->
  <rect x="570" y="840" width="100" height="50" fill="#805ad5" stroke="#6b46c1" stroke-width="2" rx="5"/>
  <text x="620" y="860" text-anchor="middle" class="text" fill="white">Job</text>
  <text x="620" y="875" text-anchor="middle" class="small-text" fill="white">生命周期控制</text>

  <!-- Dispatcher -->
  <rect x="690" y="840" width="100" height="50" fill="#f6ad55" stroke="#ed8936" stroke-width="2" rx="5"/>
  <text x="740" y="860" text-anchor="middle" class="text" fill="white">Dispatcher</text>
  <text x="740" y="875" text-anchor="middle" class="small-text" fill="white">线程调度</text>

  <!-- CoroutineName -->
  <rect x="810" y="840" width="100" height="50" fill="#4fd1c7" stroke="#38b2ac" stroke-width="2" rx="5"/>
  <text x="860" y="860" text-anchor="middle" class="text" fill="white">Name</text>
  <text x="860" y="875" text-anchor="middle" class="small-text" fill="white">协程命名</text>

  <!-- ExceptionHandler -->
  <rect x="930" y="840" width="100" height="50" fill="#f687b3" stroke="#ed64a6" stroke-width="2" rx="5"/>
  <text x="980" y="860" text-anchor="middle" class="text" fill="white">Handler</text>
  <text x="980" y="875" text-anchor="middle" class="small-text" fill="white">异常处理</text>

  <!-- 上下文组合示例 -->
  <text x="570" y="910" class="small-text">launch(Dispatchers.IO + Job() + </text>
  <text x="570" y="925" class="small-text">  CoroutineName("ImageProcess") +</text>
  <text x="570" y="940" class="small-text">  CoroutineExceptionHandler { })</text>

  <!-- 协程作用域详解 -->
  <rect x="1050" y="800" width="500" height="160" fill="#f7fafc" stroke="#a0aec0" stroke-width="2" rx="8"/>
  <text x="1300" y="820" text-anchor="middle" class="subtitle">协程作用域 (CoroutineScope)</text>

  <!-- GlobalScope -->
  <rect x="1070" y="840" width="110" height="35" fill="#fc8181" stroke="#e53e3e" stroke-width="2" rx="5"/>
  <text x="1125" y="862" text-anchor="middle" class="small-text" fill="white">GlobalScope</text>

  <!-- MainScope -->
  <rect x="1200" y="840" width="110" height="35" fill="#667eea" stroke="#5a67d8" stroke-width="2" rx="5"/>
  <text x="1255" y="862" text-anchor="middle" class="small-text" fill="white">MainScope</text>

  <!-- lifecycleScope -->
  <rect x="1330" y="840" width="110" height="35" fill="#68d391" stroke="#48bb78" stroke-width="2" rx="5"/>
  <text x="1385" y="862" text-anchor="middle" class="small-text" fill="white">lifecycleScope</text>

  <!-- viewModelScope -->
  <rect x="1460" y="840" width="110" height="35" fill="#f6ad55" stroke="#ed8936" stroke-width="2" rx="5"/>
  <text x="1515" y="862" text-anchor="middle" class="small-text" fill="white">viewModelScope</text>

  <!-- 作用域特点 -->
  <text x="1070" y="890" class="small-text">应用级别</text>
  <text x="1070" y="905" class="small-text">需手动取消</text>
  <text x="1070" y="920" class="small-text">慎用！</text>

  <text x="1200" y="890" class="small-text">主线程作用域</text>
  <text x="1200" y="905" class="small-text">Service中使用</text>
  <text x="1200" y="920" class="small-text">手动管理</text>

  <text x="1330" y="890" class="small-text">组件生命周期</text>
  <text x="1330" y="905" class="small-text">自动取消</text>
  <text x="1330" y="920" class="small-text">Activity/Fragment</text>

  <text x="1460" y="890" class="small-text">ViewModel生命周期</text>
  <text x="1460" y="905" class="small-text">onCleared取消</text>
  <text x="1460" y="920" class="small-text">推荐使用</text>

  <!-- 协程取消机制 -->
  <text x="800" y="1000" text-anchor="middle" class="subtitle">协程取消与异常处理机制</text>

  <!-- 取消方式 -->
  <rect x="50" y="1020" width="350" height="140" fill="#fffaf0" stroke="#ed8936" stroke-width="2" rx="8"/>
  <text x="225" y="1040" text-anchor="middle" class="subtitle">协程取消方式</text>

  <text x="70" y="1065" class="text">1. 自动取消 (Automatic)</text>
  <text x="90" y="1080" class="small-text">• 作用域销毁时自动取消所有子协程</text>
  <text x="90" y="1095" class="small-text">• lifecycleScope.cancel()</text>
  <text x="90" y="1110" class="small-text">• viewModelScope.cancel()</text>

  <text x="70" y="1130" class="text">2. 手动取消 (Manual)</text>
  <text x="90" y="1145" class="small-text">• job.cancel() / job.cancelAndJoin()</text>

  <!-- 异常处理 -->
  <rect x="420" y="1020" width="350" height="140" fill="#fed7d7" stroke="#fc8181" stroke-width="2" rx="8"/>
  <text x="595" y="1040" text-anchor="middle" class="subtitle">异常处理策略</text>

  <text x="440" y="1065" class="text">1. try-catch 包装</text>
  <text x="460" y="1080" class="small-text">• 防止协程异常导致应用崩溃</text>
  <text x="460" y="1095" class="small-text">• 局部异常处理</text>

  <text x="440" y="1115" class="text">2. CoroutineExceptionHandler</text>
  <text x="460" y="1130" class="small-text">• 全局异常处理器</text>
  <text x="460" y="1145" class="small-text">• 未捕获异常的最后防线</text>

  <!-- 协程状态管理 -->
  <rect x="790" y="1020" width="350" height="140" fill="#e0f2fe" stroke="#4fd1c7" stroke-width="2" rx="8"/>
  <text x="965" y="1040" text-anchor="middle" class="subtitle">协程状态管理</text>

  <text x="810" y="1065" class="text">Job 状态流转:</text>
  <text x="830" y="1080" class="small-text">New → Active → Completing → Completed</text>
  <text x="830" y="1095" class="small-text">     ↓</text>
  <text x="830" y="1110" class="small-text">   Cancelled</text>

  <text x="810" y="1130" class="text">状态检查:</text>
  <text x="830" y="1145" class="small-text">isActive, isCancelled, isCompleted</text>

  <!-- 资源清理 -->
  <rect x="1160" y="1020" width="390" height="140" fill="#e6fffa" stroke="#68d391" stroke-width="2" rx="8"/>
  <text x="1355" y="1040" text-anchor="middle" class="subtitle">资源清理策略</text>

  <text x="1180" y="1065" class="text">1. invokeOnCompletion 回调</text>
  <text x="1200" y="1080" class="small-text">• 协程完成时执行清理逻辑</text>
  <text x="1200" y="1095" class="small-text">• 无论正常完成还是取消都会执行</text>

  <text x="1180" y="1115" class="text">2. finally 块保证</text>
  <text x="1200" y="1130" class="small-text">• 确保资源释放</text>
  <text x="1200" y="1145" class="small-text">• native资源、文件句柄、网络连接</text>

  <!-- 连接线显示关系 -->
  <line x1="200" y1="200" x2="520" y2="100" class="arrow dashed"/>
  <line x1="270" y1="200" x2="750" y2="100" class="arrow dashed"/>
  <line x1="270" y1="200" x2="980" y2="100" class="arrow dashed"/>

</svg>
