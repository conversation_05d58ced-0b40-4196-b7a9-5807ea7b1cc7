//
// Created by knight on 2023/7/21.
//

#ifndef APP_NIR_TRACKER_CALIBRATION_H
#define APP_NIR_TRACKER_CALIBRATION_H

#include "utils.h"
#include <Eigen/Dense>

using namespace Eigen;

using std::string;
using namespace chrono;
typedef std::chrono::high_resolution_clock Clock;

#define calibration_number 9
#define consist_num_threshold 3
#define point_move_threshold 10

#define num_capture_params_min 5

#define delay_time 500  // ms
#define tolerance_first_time 4000  // ms
#define tolerance_second_time 8000  // ms

struct keypoint_parameter //定义关键点坐标结构体，包括瞳孔中心点坐标、普尔钦斑点坐标
{
    float pupil_x;        // 瞳孔中心 x
    float pupil_y;        // 瞳孔中心 y
    float light_p_lx;     // 普尔钦斑 左 x
    float light_p_ly;     // 普尔钦斑 左 y
    float light_p_rx;     // 普尔钦斑 右 x
    float light_p_ry;     // 普尔钦斑 右 y

    float eye_dist;       // 瞳距

    // 定义带参数构造函数
    keypoint_parameter(cv::Point2f cent, cv::Point2f left_p, cv::Point2f right_p, float eye_dist_) : pupil_x(cent.x),
    pupil_y(cent.y),  light_p_lx(left_p.x), light_p_ly(left_p.y), light_p_rx(right_p.x), light_p_ry(right_p.y), eye_dist(eye_dist_){}
    // 定义无参数构造函数
    keypoint_parameter() : pupil_x(0), pupil_y(0), light_p_lx(0), light_p_ly(0), light_p_rx(0),  light_p_ry(0), eye_dist(-1){}

    //赋值运算符重载
    keypoint_parameter& operator=(const keypoint_parameter& other) {
        // 检查自我赋值
        if (this != &other) {
            pupil_x = other.pupil_x;
            pupil_y = other.pupil_y;
            light_p_lx = other.light_p_lx;
            light_p_ly = other.light_p_ly;
            light_p_rx = other.light_p_rx;
            light_p_ry = other.light_p_ry;
            eye_dist = other.eye_dist;
        }
        return *this;              // 返回当前对象的引用，支持链式赋值
    }

    // 减法运算符重载
    float operator-(const keypoint_parameter& other) const {
        float value;
        value = abs(pupil_x - other.pupil_x) +
                abs(pupil_y - other.pupil_y) +
                abs(light_p_lx - other.light_p_lx) +
                abs(light_p_ly - other.light_p_ly) +
                abs(light_p_rx - other.light_p_rx) +
                abs(light_p_ry - other.light_p_ry);
        return value;
    }
};

struct screen_point   // 定义屏幕的相对点坐标结构体
{
    float screen_x;
    float screen_y;
    screen_point(): screen_x(-1.0F), screen_y(-1.0F){}
};

/**
 * calibration each eye
 */

class Eye_state {
public:
    Eye_state(int* idx_list, float(*target_points)[2]);
    ~Eye_state();

private:
    int* point_index_order;
    float(*calibrate_points)[2];

    keypoint_parameter pre_det_points;
    bool not_finish_capture_data;
    bool had_compute_flag;
    int consist_num;
    vector<keypoint_parameter> calib_params_data_vec;
    vector<screen_point> calib_points_data_vec;

    static vector<float> solveLeastSquares(const vector<vector<float>>& data);
    void calculate_feature_vector_func(vector<vector<float>>& crs_vector_x, vector<vector<float>>& left_vector_x, vector<vector<float>>& right_vector_x,
                                       vector<vector<float>>& crs_vector_y, vector<vector<float>>& left_vector_y, vector<vector<float>>& right_vector_y);

public:
    // function
    void reset_calib_parameter();
    void get_computed_params_flag(bool& finish_flag) const;
    void set_finished_capture_flag();
    void compare_calib_parameter_consist(const cv::Point2f& pupil_cent, const vector<cv::Point2f>& point_crs, float eye_dist,
                                         bool& finish_one_point_flag, bool& data_isready, int& return_consist_num,
                                         int valid_num, int point_index, int light_num_thres);
    void compute_Matrix_func(vector<vector<float>>& warpMatrix_data, float& score);
    void get_calib_original_params_func(vector<vector<double>>& params_vec);
};

/**
 * calibration two eye gaze
 */
class Calibrator {

public:
    Calibrator(int* idx_list, float(*target_points)[2]);
    ~Calibrator();

private:
    int* point_index_order;
    float(*calibrate_points)[2];

    Eye_state calib_left_class;
    Eye_state calib_right_class;

    int win_height = 1080;
    int win_width = 1920;

    int point_index;
    int left_consist_num;  //每个校准点的一致性进度 {1,2,3}
    int right_consist_num; //每个校准点的一致性进度 {1,2,3}
    int crs_num_threshold;  // 1 or 2

    // --- calibration state ---
    bool finish_calibrating;
    bool left_finish_one_flag;
    bool right_finish_one_flag;
    bool data_isready;

    float score_left;
    float score_right;

    std::vector<std::vector<double>> double_left_DetData;
    std::vector<std::vector<double>> double_right_DetData;
    std::vector<std::vector<double>> double_left_params;
    std::vector<std::vector<double>> double_right_params;

    vector<vector<float>> left_warpMatrix_data;
    vector<vector<float>> right_warpMatrix_data;

    // --- record time ---
    chrono::time_point<steady_clock, nanoseconds> left_clock_pre;        // record left time clock
    chrono::time_point<steady_clock, nanoseconds> right_clock_pre;       // record right time clock
    chrono::time_point<steady_clock, nanoseconds> time_count_one_point;  // record cost time of one point

public:
    void reset_calibrate_params();
    bool setup_calibrate_point_state(int capture_index);
    vector<int>  run_calibrating(const frame_detection_result& det_result, bool& calib_next_point, bool& finish_calib, bool& calibrate_succeed, bool* succeed_list);
    float get_calibrate_score_func() const;
    calibration_params_data_str get_calibrate_data_and_params_func();
};

#endif //APP_NIR_TRACKER_CALIBRATION_H
