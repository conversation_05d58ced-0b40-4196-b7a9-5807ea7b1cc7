<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #e74c3c; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .text-ops { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .image-ops { fill: #fff3cd; stroke: #f39c12; stroke-width: 2; }
      .layout-ops { fill: #e3f2fd; stroke: #3498db; stroke-width: 2; }
      .anim-ops { fill: #f3e5f5; stroke: #9b59b6; stroke-width: 2; }
      .native-ops { fill: #ffeaa7; stroke: #fdcb6e; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">🔧 View操作的详细实现机制</text>
  
  <!-- TextView操作详解 -->
  <rect x="50" y="60" width="850" height="250" rx="15" class="text-ops"/>
  <text x="475" y="85" text-anchor="middle" class="subtitle">📝 TextView 操作详解</text>
  
  <text x="60" y="110" class="text">🔤 <tspan class="subtitle">1. 文本内容设置:</tspan></text>
  <text x="60" y="130" class="code">textView.text = "直接设置文本内容"</text>
  <text x="60" y="150" class="code">textView.setText("使用方法设置")</text>
  <text x="60" y="170" class="code">textView.setText(R.string.str_title)  // 使用资源ID</text>
  
  <text x="450" y="110" class="text">🎨 <tspan class="subtitle">2. 文本样式设置:</tspan></text>
  <text x="450" y="130" class="code">textView.textSize = 16f  // 设置字体大小</text>
  <text x="450" y="150" class="code">textView.setTextColor(Color.RED)  // 设置颜色</text>
  <text x="450" y="170" class="code">textView.typeface = Typeface.BOLD  // 设置字体样式</text>
  
  <text x="60" y="200" class="text">📐 <tspan class="subtitle">3. 可见性控制:</tspan></text>
  <text x="60" y="220" class="code">textView.isVisible = true/false  // Kotlin扩展</text>
  <text x="60" y="240" class="code">textView.visibility = View.VISIBLE/GONE/INVISIBLE</text>
  <text x="60" y="260" class="code">// VISIBLE=可见, GONE=不占空间, INVISIBLE=占空间但不可见</text>
  
  <text x="450" y="200" class="text">🔧 <tspan class="subtitle">4. 特殊文本处理:</tspan></text>
  <text x="450" y="220" class="code">val spannableString = SpannableString(text)</text>
  <text x="450" y="240" class="code">spannableString.setSpan(ForegroundColorSpan(color), start, end)</text>
  <text x="450" y="260" class="code">textView.text = spannableString  // 富文本显示</text>
  
  <text x="60" y="290" class="small-text">💡 实际案例: tvUserName.text = user?.username, tvUserPhone.isVisible = user?.phone.isNotBlank()</text>

  <!-- ImageView操作详解 -->
  <rect x="920" y="60" width="830" height="250" rx="15" class="image-ops"/>
  <text x="1335" y="85" text-anchor="middle" class="subtitle">🖼️ ImageView 操作详解</text>
  
  <text x="930" y="110" class="text">🎯 <tspan class="subtitle">1. 图片资源设置:</tspan></text>
  <text x="930" y="130" class="code">imageView.setImageResource(R.drawable.icon)  // 设置资源图片</text>
  <text x="930" y="150" class="code">imageView.setImageBitmap(bitmap)  // 设置位图</text>
  <text x="930" y="170" class="code">imageView.setImageDrawable(drawable)  // 设置Drawable</text>
  
  <text x="930" y="200" class="text">🌐 <tspan class="subtitle">2. 网络图片加载:</tspan></text>
  <text x="930" y="220" class="code">ImageLoader.loadImageWithPlaceholder(context,</text>
  <text x="930" y="240" class="code">  url, placeholder, error, imageView)</text>
  <text x="930" y="260" class="code">// 异步加载，带占位图和错误图</text>
  
  <text x="1320" y="110" class="text">📐 <tspan class="subtitle">3. 缩放模式:</tspan></text>
  <text x="1320" y="130" class="code">imageView.scaleType = ImageView.ScaleType.CENTER_CROP</text>
  <text x="1320" y="150" class="code">// CENTER_CROP: 等比缩放填满</text>
  <text x="1320" y="170" class="code">// FIT_CENTER: 等比缩放适应</text>
  
  <text x="1320" y="200" class="text">🎨 <tspan class="subtitle">4. 背景设置:</tspan></text>
  <text x="1320" y="220" class="code">imageView.setBackgroundResource(R.drawable.bg)</text>
  <text x="1320" y="240" class="code">imageView.background = null  // 清除背景</text>
  <text x="1320" y="260" class="code">imageView.setBackgroundColor(Color.WHITE)</text>
  
  <text x="930" y="290" class="small-text">💡 实际案例: 用户头像根据性别显示不同默认图片，二维码加载失败显示刷新图标</text>

  <!-- 布局参数操作详解 -->
  <rect x="50" y="330" width="850" height="280" rx="15" class="layout-ops"/>
  <text x="475" y="355" text-anchor="middle" class="subtitle">📐 布局参数操作详解</text>
  
  <text x="60" y="380" class="text">📏 <tspan class="subtitle">1. LayoutParams 设置:</tspan></text>
  <text x="60" y="400" class="code">val params = view.layoutParams as ConstraintLayout.LayoutParams</text>
  <text x="60" y="420" class="code">params.width = 100.dp2px(context)  // 设置宽度</text>
  <text x="60" y="440" class="code">params.height = LayoutParams.WRAP_CONTENT  // 自适应高度</text>
  <text x="60" y="460" class="code">view.layoutParams = params  // 应用参数</text>
  
  <text x="60" y="485" class="text">🔗 <tspan class="subtitle">2. ConstraintSet 约束:</tspan></text>
  <text x="60" y="505" class="code">val constraintSet = ConstraintSet()</text>
  <text x="60" y="525" class="code">constraintSet.clone(parentLayout)  // 克隆当前约束</text>
  <text x="60" y="545" class="code">constraintSet.connect(viewId, ConstraintSet.TOP, parentId, ConstraintSet.TOP)</text>
  <text x="60" y="565" class="code">constraintSet.setMargin(viewId, ConstraintSet.TOP, 20.dp2px(context))</text>
  <text x="60" y="585" class="code">constraintSet.applyTo(parentLayout)  // 应用约束</text>
  
  <text x="450" y="380" class="text">🪟 <tspan class="subtitle">3. WindowManager 操作:</tspan></text>
  <text x="450" y="400" class="code">val params = WindowManager.LayoutParams(</text>
  <text x="450" y="420" class="code">  width, height, TYPE_APPLICATION_OVERLAY,</text>
  <text x="450" y="440" class="code">  FLAG_NOT_FOCUSABLE, PixelFormat.TRANSLUCENT)</text>
  <text x="450" y="460" class="code">windowManager.addView(view, params)  // 添加悬浮窗</text>
  <text x="450" y="480" class="code">windowManager.updateViewLayout(view, params)  // 更新位置</text>
  <text x="450" y="500" class="code">windowManager.removeView(view)  // 移除悬浮窗</text>
  
  <text x="450" y="525" class="text">📱 <tspan class="subtitle">4. 动态添加View:</tspan></text>
  <text x="450" y="545" class="code">parentLayout.addView(childView, layoutParams)</text>
  <text x="450" y="565" class="code">parentLayout.removeView(childView)</text>
  <text x="450" y="585" class="code">parentLayout.removeAllViews()  // 清空所有子View</text>
  
  <text x="60" y="600" class="small-text">💡 实际案例: DotView悬浮窗位置更新，全屏/半屏切换时的约束调整</text>

  <!-- 动画操作详解 -->
  <rect x="920" y="330" width="830" height="280" rx="15" class="anim-ops"/>
  <text x="1335" y="355" text-anchor="middle" class="subtitle">🎬 动画操作详解</text>
  
  <text x="930" y="380" class="text">🔄 <tspan class="subtitle">1. ObjectAnimator 属性动画:</tspan></text>
  <text x="930" y="400" class="code">val rotateAnim = ObjectAnimator.ofFloat(view, "rotation", 0f, 360f)</text>
  <text x="930" y="420" class="code">rotateAnim.duration = 2000  // 持续时间</text>
  <text x="930" y="440" class="code">rotateAnim.repeatCount = ValueAnimator.INFINITE  // 无限循环</text>
  <text x="930" y="460" class="code">rotateAnim.interpolator = LinearInterpolator()  // 线性插值</text>
  <text x="930" y="480" class="code">rotateAnim.start()  // 开始动画</text>
  
  <text x="930" y="505" class="text">📍 <tspan class="subtitle">2. 位移动画:</tspan></text>
  <text x="930" y="525" class="code">ObjectAnimator.ofFloat(view, "translationY", 0f, 100f)</text>
  <text x="930" y="545" class="code">ObjectAnimator.ofFloat(view, "translationX", startX, endX)</text>
  <text x="930" y="565" class="code">// translationY: Y轴位移, translationX: X轴位移</text>
  
  <text x="1320" y="380" class="text">🎯 <tspan class="subtitle">3. 动画监听:</tspan></text>
  <text x="1320" y="400" class="code">animator.addUpdateListener { animation -></text>
  <text x="1320" y="420" class="code">  val value = animation.animatedValue as Float</text>
  <text x="1320" y="440" class="code">  // 实时获取动画值进行处理</text>
  <text x="1320" y="460" class="code">}</text>
  
  <text x="1320" y="485" class="text">⏸️ <tspan class="subtitle">4. 动画控制:</tspan></text>
  <text x="1320" y="505" class="code">animator.start()    // 开始</text>
  <text x="1320" y="525" class="code">animator.pause()    // 暂停</text>
  <text x="1320" y="545" class="code">animator.resume()   // 恢复</text>
  <text x="1320" y="565" class="code">animator.cancel()   // 取消</text>
  
  <text x="930" y="590" class="small-text">💡 实际案例: 加载动画旋转，扫码动画上下移动，二维码加载时的旋转效果</text>

  <!-- 事件处理详解 -->
  <rect x="50" y="630" width="850" height="200" rx="15" class="native-ops"/>
  <text x="475" y="655" text-anchor="middle" class="subtitle">👆 事件处理详解</text>
  
  <text x="60" y="680" class="text">🖱️ <tspan class="subtitle">1. 点击事件:</tspan></text>
  <text x="60" y="700" class="code">view.setOnClickListener { /* 处理点击 */ }</text>
  <text x="60" y="720" class="code">view.setOnSingleClickListener { /* 防重复点击 */ }</text>
  <text x="60" y="740" class="code">view.setOnLongClickListener { /* 长按事件 */ }</text>
  
  <text x="450" y="680" class="text">⌨️ <tspan class="subtitle">2. 输入事件:</tspan></text>
  <text x="450" y="700" class="code">editText.setOnEditorActionListener { view, actionId, event -></text>
  <text x="450" y="720" class="code">  if (actionId == EditorInfo.IME_ACTION_DONE) {</text>
  <text x="450" y="740" class="code">    // 处理完成按钮点击</text>
  <text x="450" y="760" class="code">  }</text>
  <text x="450" y="780" class="code">}</text>
  
  <text x="60" y="765" class="text">🔄 <tspan class="subtitle">3. 状态变化:</tspan></text>
  <text x="60" y="785" class="code">switch.setOnCheckedChangeListener { _, isChecked -></text>
  <text x="60" y="805" class="code">  // 处理开关状态变化</text>
  <text x="60" y="825" class="code">}</text>
  
  <text x="450" y="800" class="small-text">💡 实际案例: 扫码按钮点击，输入框完成事件，设置开关状态变化</text>

  <!-- 焦点和键盘控制 -->
  <rect x="920" y="630" width="830" height="200" rx="15" class="layout-ops"/>
  <text x="1335" y="655" text-anchor="middle" class="subtitle">⌨️ 焦点和键盘控制</text>
  
  <text x="930" y="680" class="text">🎯 <tspan class="subtitle">1. 焦点控制:</tspan></text>
  <text x="930" y="700" class="code">view.requestFocus()  // 请求焦点</text>
  <text x="930" y="720" class="code">view.clearFocus()    // 清除焦点</text>
  <text x="930" y="740" class="code">view.isFocusable = true/false  // 设置是否可获得焦点</text>
  
  <text x="1320" y="680" class="text">⌨️ <tspan class="subtitle">2. 键盘控制:</tspan></text>
  <text x="1320" y="700" class="code">hideKeyboard(view)  // 隐藏软键盘</text>
  <text x="1320" y="720" class="code">showKeyboard(view)  // 显示软键盘</text>
  <text x="1320" y="740" class="code">// 通常在InputMethodManager中实现</text>
  
  <text x="930" y="765" class="text">🔧 <tspan class="subtitle">3. NumberPicker 特殊控制:</tspan></text>
  <text x="930" y="785" class="code">numberPicker.minValue = 3</text>
  <text x="930" y="805" class="code">numberPicker.maxValue = 100</text>
  <text x="930" y="825" class="code">numberPicker.wrapSelectorWheel = false  // 不循环</text>
  
  <text x="1320" y="765" class="text">📱 <tspan class="subtitle">4. WebView 控制:</tspan></text>
  <text x="1320" y="785" class="code">webView.evaluateJavascript("javascript:function()", null)</text>
  <text x="1320" y="805" class="code">webView.addJavascriptInterface(object, "android")</text>
  <text x="1320" y="825" class="code">// JavaScript与Android交互</text>

  <!-- Native层操作 -->
  <rect x="50" y="850" width="1700" height="180" rx="15" class="native-ops"/>
  <text x="900" y="875" text-anchor="middle" class="subtitle">🔧 Native层View操作</text>
  
  <text x="60" y="900" class="text">🎨 <tspan class="subtitle">1. ANativeWindow 直接绘制:</tspan></text>
  <text x="60" y="920" class="code">ANativeWindow_setBuffersGeometry(window, width, height, WINDOW_FORMAT_RGBA_8888)</text>
  <text x="60" y="940" class="code">ANativeWindow_lock(window, &buffer, nullptr)  // 锁定缓冲区</text>
  <text x="60" y="960" class="code">memcpy(dstData + i * dstlineSize, srcData + i * srclineSize, srclineSize)  // 逐行拷贝像素</text>
  <text x="60" y="980" class="code">ANativeWindow_unlockAndPost(window)  // 解锁并提交显示</text>
  
  <text x="900" y="900" class="text">👁️ <tspan class="subtitle">2. OpenCV 图像处理:</tspan></text>
  <text x="900" y="920" class="code">cv::resize(srcImg, dstImg, cv::Size(width, height))  // 图像缩放</text>
  <text x="900" y="940" class="code">cv::cvtColor(grayImg, bgrImg, COLOR_GRAY2BGR)  // 颜色空间转换</text>
  <text x="900" y="960" class="code">cv::rectangle(img, rect, Scalar(255, 0, 0), 2)  // 绘制矩形</text>
  <text x="900" y="980" class="code">// 直接在图像上绘制检测框、标记点等</text>
  
  <text x="60" y="1010" class="small-text">💡 Native层操作主要用于高性能图像处理和实时渲染，如眼动追踪的视点显示、人脸检测框绘制等</text>

  <!-- 最佳实践总结 -->
  <rect x="50" y="1050" width="1700" height="320" rx="15" class="text-ops"/>
  <text x="900" y="1075" text-anchor="middle" class="subtitle">💡 View操作最佳实践总结</text>
  
  <text x="60" y="1100" class="text">🧵 <tspan class="subtitle">1. 线程安全原则:</tspan></text>
  <text x="60" y="1120" class="small-text">   • 所有View操作必须在主线程(UI线程)执行</text>
  <text x="60" y="1140" class="small-text">   • 子线程中使用 runOnUiThread { } 或 Handler.post { } 切换到主线程</text>
  <text x="60" y="1160" class="small-text">   • 协程中使用 withContext(Dispatchers.Main) { } 切换到主线程</text>
  
  <text x="450" y="1100" class="text">⚡ <tspan class="subtitle">2. 性能优化:</tspan></text>
  <text x="450" y="1120" class="small-text">   • 避免频繁的布局操作，批量更新View属性</text>
  <text x="450" y="1140" class="small-text">   • 使用 ViewBinding 替代 findViewById，提高性能和类型安全</text>
  <text x="450" y="1160" class="small-text">   • 图片加载使用异步方式，避免阻塞主线程</text>
  
  <text x="900" y="1100" class="text">🛡️ <tspan class="subtitle">3. 内存安全:</tspan></text>
  <text x="900" y="1120" class="small-text">   • 及时释放动画资源，避免内存泄漏</text>
  <text x="900" y="1140" class="small-text">   • 悬浮窗等系统级View要正确移除</text>
  <text x="900" y="1160" class="small-text">   • 使用生命周期感知组件管理View状态</text>
  
  <text x="1300" y="1100" class="text">🎯 <tspan class="subtitle">4. 用户体验:</tspan></text>
  <text x="1300" y="1120" class="small-text">   • 提供加载状态和错误提示</text>
  <text x="1300" y="1140" class="small-text">   • 使用动画让状态变化更自然</text>
  <text x="1300" y="1160" class="small-text">   • 防重复点击，避免误操作</text>
  
  <text x="60" y="1185" class="text">🔧 <tspan class="subtitle">5. 代码组织:</tspan></text>
  <text x="60" y="1205" class="small-text">   • 将View操作封装在专门的方法中，如 updateUserInfo(), updateDeviceInfo()</text>
  <text x="60" y="1225" class="small-text">   • 使用扩展函数简化常用操作，如 isVisible 属性</text>
  <text x="60" y="1245" class="small-text">   • 统一的错误处理和异常捕获机制</text>
  
  <text x="900" y="1185" class="text">🧪 <tspan class="subtitle">6. 测试友好:</tspan></text>
  <text x="900" y="1205" class="small-text">   • View操作与业务逻辑分离，便于单元测试</text>
  <text x="900" y="1225" class="small-text">   • 使用 Espresso 进行UI自动化测试</text>
  <text x="900" y="1245" class="small-text">   • 提供View状态的可观测性</text>
  
  <text x="60" y="1270" class="text">📊 <tspan class="subtitle">典型View更新流程:</tspan></text>
  <text x="60" y="1290" class="small-text">数据变化 → LiveData通知 → Observer回调 → updateXXX()方法 → 具体View操作 → 界面重绘 → 用户看到变化</text>
  
  <text x="60" y="1315" class="text">🎯 <tspan class="subtitle">实际应用场景:</tspan></text>
  <text x="60" y="1335" class="small-text">• 用户信息显示: 头像加载、文本设置、可见性控制</text>
  <text x="60" y="1355" class="small-text">• 设备状态更新: 背景切换、Fragment替换、约束调整</text>
  <text x="500" y="1335" class="small-text">• 动画效果: 加载旋转、扫码移动、全屏切换</text>
  <text x="500" y="1355" class="small-text">• 悬浮显示: 眼动追踪点、进度提示、错误弹窗</text>

</svg>
