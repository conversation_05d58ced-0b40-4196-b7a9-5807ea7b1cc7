//
// Created by user on 2024/12/30.
//

#include "app_glance.h"

GazeGlance::GazeGlance()
{
    target_point = Point2f(-1.0F, -1.0F);
    gaze_data_list.clear();
}

GazeGlance::~GazeGlance(){}

void GazeGlance::reset_params_func()
{
    target_point = Point2f(-1.0F, -1.0F);
    gaze_data_list.clear();
}

// 设置注视的目标点位置
bool GazeGlance::set_target_point(float x, float y)
{
    bool flag = false;
    if(x > 0 && x < 1.0 && y > 0 && y < 1.0)
    {
        target_point = Point2f(x, y);
        LOGI("------- GazeGlance::set_target_point x : %f , y : %f !!!! ------", target_point.x,target_point.y);
        // 获取当前时间点
        auto now = std::chrono::system_clock::now();
        // 将时间点转换为时间戳（毫秒精度）
        startTime = std::chrono::time_point_cast<std::chrono::milliseconds>(now).time_since_epoch().count();
        flag = true;
    }
    return flag;
}

// 应用期间传输数据
bool GazeGlance::collect_data(float x, float y, float duration)
{
    bool flag = false;
    if(x > 0 && x < 1.0 && y > 0 && y < 1.0)
    {
        float dist = sqrtf(powf(target_point.x - x,2) + powf(target_point.y - y,2));
        if(dist < app_hit_near_thres)
        {
            LOGI("------- GazeGlance::collect_data x : %f , y : %f !!!! ------", target_point.x,target_point.y);
            // 获取当前时间点
            auto now = std::chrono::system_clock::now();
            // 将时间点转换为时间戳（毫秒精度）
            endTime = std::chrono::time_point_cast<std::chrono::milliseconds>(now).time_since_epoch().count();
            gaze_data_list.emplace_back(Point3f(target_point.x, target_point.y, static_cast<int>(endTime - startTime)));
            flag = true;
        }
    }
    return flag;
}

// 处理眼动轨迹的数据
std::vector<std::vector<Point3f>> GazeGlance::postprocess_trajectory_data()
{
    std::vector<std::vector<Point3f>> stare_points;
    if(!gaze_data_list.empty())
    {
        int num_p = (int)gaze_data_list.size();
        LOGI("------- GazeGlance::postprocess_trajectory_data num_p : %d !!!! ------", num_p);
//        Point3f pre_p = gaze_data_list[0];
        vector<Point3f> line_points = {};
        for (int i = 0; i < num_p; i++)
        {
//            Point3f p1 = gaze_data_list[i - 1];
//            Point3f p2 = gaze_data_list[i];
//            float grad1 = abs(p2.x - p1.x) + abs(p2.y - p1.y);
//            float grad2 = abs(p2.x - pre_p.x) + abs(p2.y - pre_p.y);
//            if(grad1 < app_filter_thres)
//            {
//                if (grad2 < app_merge_thres)
//                {
//                    line_poins.push_back(p2);
//                }
//                else
//                {
//                    pre_p = p2;
//                    stare_points.push_back(line_poins);
//                    line_poins.clear();
//                    line_poins.push_back(pre_p);
//                }
//            }
            line_points.push_back(gaze_data_list[i]);
            stare_points.push_back(line_points);
            line_points.clear();
        }
    }
    return stare_points;
}

// 获取json的数据
string GazeGlance::get_record_jsondata()
{
    string json_data = "{'gaze': []}";
    std::vector<std::vector<Point3f>> stare_points = postprocess_trajectory_data();
    LOGI("------- GazeGlance::get_record_jsondata num_p : %d !!!! ------", (int)stare_points.size());
    if(!stare_points.empty())
    {
        json_data = transfer_trajectory_to_jsondata(stare_points);
    }
    return json_data;
}
