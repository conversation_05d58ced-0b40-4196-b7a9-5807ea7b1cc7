//
// Created by knight on 2023/6/9.
//

#include "opencv2/video/tracking.hpp"
#include "opencv2/highgui/highgui.hpp"
#include <cstdio>

using namespace cv;
using namespace std;

#ifndef GAZE_TRACKER_KALMANFILTER_H
#define GAZE_TRACKER_KALMANFILTER_H


class CoordinateKmf {

public:
    CoordinateKmf(int state_num, int measure_num);
    ~CoordinateKmf();

public:
    int stateNum;
    int measureNum;

    Point2f predict_pt;          // predict point by kalmanfilter
    Mat measurement;           // measure by gaze

    KalmanFilter *KF;

public:
    void update(float x, float y);
};

#endif //GAZE_TRACKER_KALMANFILTER_H