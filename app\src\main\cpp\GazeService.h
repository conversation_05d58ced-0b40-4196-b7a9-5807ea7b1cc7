//
// Created by user on 2024/12/13.
//

#ifndef MIT_DD_DEMO_GAZESERVICE_H
#define MIT_DD_DEMO_GAZESERVICE_H
#include <android/native_window_jni.h>
#include <pthread.h>
#include <random>
#include "detection.h"
#include "GazeTracker.h"
#include "GazeCalibrate.h"
#include "poseAlign.h"
#include "saveVideos.h"
#include "utils.h"

using namespace cv;
using namespace std;
using namespace chrono;

typedef std::chrono::high_resolution_clock Clock;

using std::string;
using std::random_device;
using std::default_random_engine;

/**
 * 眼动服务
 */
class GazeService {

public:
    GazeService(const char *config_dir, const char* sn_serial);
    ~GazeService();

public:
    // Surface
    pthread_mutex_t mutex{}; // 互斥锁
    ANativeWindow *window = nullptr; // ANativeWindow 用来渲染画面的 == Surface对象

    // visual
    Mat pose_align_mask_img;    // 姿势参考图片
    Mat visual_detect_img;      // 显示检测和眼动效果
    Mat visual_pose_img;        // 显示姿势校准过程

private:
    string sn_serial_number;
    // 模块
    Detection detector;              // 检测模块
    GazeTracker gaze_tracker;        // 视线跟踪模块
    GazeCalibrate gaze_calibrator;   // 眼动校准模块
    PoseAlign pose_align_class;      // 姿势校准模块
    SaveVideos dataWriter;           // 数据保存模块

    // processing state
    int processing_state;            // 记录处理状态【姿势、校准、跟踪】
    bool saving_videos;              // 正在保存视频数据的标志

    // 保存文件路径
    string posealign_imgname = "/reading.jpg";
    string calibration_txtname = "/calib_param.txt";

    string config_filepath;

    // record eye state
    frame_detection_result detect_result;       // 关键点检测结果
    gazetracker_result_str gaze_result;         // 眼动跟踪结果
    pose_align_result pose_result;              // 姿势校准结果
    calibrating_result_str calib_result;        // 眼动校准结果

    mapping_params_str gaze_params;

    // parameters


public:
    // visual
    void setANativeWindow(ANativeWindow *window);  //设置surface
    void draw(const Mat& mat);   //渲染
    // ============ state ===============
    bool get_tracker_state_func() const;
    void update_tracker_config_func();
    // ==================================

    //设置 GazeTrack callback
    void setGazeTrackCallback(JNIEnv* env, jobject callback_calib);

    // ========== command =============
    bool start_tracking();                 // 启动跟踪
    bool stop_tracking();                  // 停止跟踪

    bool start_aligning_head_pose(bool calib);       // 启动姿势校准
    bool stop_aligning_head_pose();                  // 停止姿势校准

    bool start_calibration();              // 启动校准
    bool stop_calibration();               // 停止校准

    void start_video_saving();             // 启动数据保存
    void stop_video_saving();              // 停止数据保存
    // ==================================

    gazetracker_result_str process_gazetracker(const cv::Mat &Image);
    pose_align_result process_poseAlign(const cv::Mat &Image);
    calibrating_result_str process_calibration(const cv::Mat &Image);

    // ================= visual =====================
    Mat visual_calibration_draw_func() const;
    Mat visual_pose_align_func(const cv::Mat &Image);
    Mat visual_tracker_result_func(const cv::Mat &Image);

};

string strRand(int length);

#endif //MIT_DD_DEMO_GAZESERVICE_H
