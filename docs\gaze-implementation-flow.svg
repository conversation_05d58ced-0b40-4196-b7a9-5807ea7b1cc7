<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #555; }
      .text { font-family: Arial, sans-serif; font-size: 10px; fill: #333; }
      .small-text { font-family: Arial, sans-serif; font-size: 8px; fill: #666; }
      .code-text { font-family: Consolas, monospace; font-size: 8px; fill: #444; }
      .step-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
      .process-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .decision-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .data-box { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .success-arrow { stroke: #4caf50; stroke-width: 2; fill: none; marker-end: url(#successhead); }
      .error-arrow { stroke: #f44336; stroke-width: 2; fill: none; marker-end: url(#errorhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <marker id="successhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50" />
    </marker>
    <marker id="errorhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f44336" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="25" text-anchor="middle" class="title">眼动追踪AAR实现流程详图</text>
  <text x="700" y="45" text-anchor="middle" class="text">Gaze Tracking AAR Implementation Flow</text>
  
  <!-- 第一阶段：初始化 -->
  <text x="50" y="80" class="subtitle">阶段1: SDK初始化 (Initialization)</text>
  
  <rect x="50" y="90" width="200" height="80" class="step-box" rx="5"/>
  <text x="60" y="110" class="text">1. 宿主应用启动</text>
  <text x="60" y="125" class="code-text">GazeTrackingSDK.getInstance()</text>
  <text x="60" y="140" class="code-text">.init(context, config)</text>
  <text x="60" y="155" class="small-text">• 检查权限</text>
  <text x="60" y="165" class="small-text">• 拷贝模型文件</text>
  
  <rect x="280" y="90" width="200" height="80" class="process-box" rx="5"/>
  <text x="290" y="110" class="text">2. 服务初始化</text>
  <text x="290" y="125" class="code-text">GazeTrackingManager.init()</text>
  <text x="290" y="140" class="small-text">• 启动GazeTrackService</text>
  <text x="290" y="150" class="small-text">• 绑定服务连接</text>
  <text x="290" y="160" class="small-text">• 初始化TrackingManager</text>
  
  <rect x="510" y="90" width="200" height="80" class="decision-box" rx="5"/>
  <text x="520" y="110" class="text">3. 权限检查</text>
  <text x="520" y="125" class="code-text">checkPermissions()</text>
  <text x="520" y="140" class="small-text">• CAMERA权限</text>
  <text x="520" y="150" class="small-text">• WRITE_EXTERNAL_STORAGE</text>
  <text x="520" y="160" class="small-text">• 相机硬件检查</text>
  
  <!-- 第二阶段：相机启动 -->
  <text x="50" y="210" class="subtitle">阶段2: 相机启动 (Camera Setup)</text>
  
  <rect x="50" y="220" width="200" height="80" class="step-box" rx="5"/>
  <text x="60" y="240" class="text">4. 相机管理器启动</text>
  <text x="60" y="255" class="code-text">GTCameraManager.startCamera()</text>
  <text x="60" y="270" class="small-text">• 选择后置摄像头(ID:0)</text>
  <text x="60" y="280" class="small-text">• 设置4048x3040分辨率</text>
  <text x="60" y="290" class="small-text">• 启用补光灯</text>
  
  <rect x="280" y="220" width="200" height="80" class="process-box" rx="5"/>
  <text x="290" y="240" class="text">5. 图像分析配置</text>
  <text x="290" y="255" class="code-text">ImageAnalysis.Builder()</text>
  <text x="290" y="270" class="small-text">• 设置分析器</text>
  <text x="290" y="280" class="small-text">• 配置回调线程</text>
  <text x="290" y="290" class="small-text">• 绑定生命周期</text>
  
  <rect x="510" y="220" width="200" height="80" class="data-box" rx="5"/>
  <text x="520" y="240" class="text">6. 图像数据流</text>
  <text x="520" y="255" class="code-text">onAnalyze(ImageProxy)</text>
  <text x="520" y="270" class="small-text">• YUV_420_888格式</text>
  <text x="520" y="280" class="small-text">• 30fps帧率</text>
  <text x="520" y="290" class="small-text">• 实时图像传递</text>
  
  <!-- 第三阶段：校准流程 -->
  <text x="750" y="80" class="subtitle">阶段3: 校准流程 (Calibration)</text>
  
  <rect x="750" y="90" width="200" height="80" class="step-box" rx="5"/>
  <text x="760" y="110" class="text">7. 姿势校准</text>
  <text x="760" y="125" class="code-text">PostureCalibrationView</text>
  <text x="760" y="140" class="small-text">• 检测人脸位置</text>
  <text x="760" y="150" class="small-text">• 验证距离(35-65cm)</text>
  <text x="760" y="160" class="small-text">• 角度校正(±15度)</text>
  
  <rect x="980" y="90" width="200" height="80" class="process-box" rx="5"/>
  <text x="990" y="110" class="text">8. 视标校准</text>
  <text x="990" y="125" class="code-text">VisualCalibrationView</text>
  <text x="990" y="140" class="small-text">• 9点校准算法</text>
  <text x="990" y="150" class="small-text">• 生成校准矩阵</text>
  <text x="990" y="160" class="small-text">• 保存校准参数</text>
  
  <rect x="750" y="220" width="200" height="80" class="decision-box" rx="5"/>
  <text x="760" y="240" class="text">9. 校准验证</text>
  <text x="760" y="255" class="code-text">checkCalibrationParam()</text>
  <text x="760" y="270" class="small-text">• 校准精度检查</text>
  <text x="760" y="280" class="small-text">• 参数有效性验证</text>
  <text x="760" y="290" class="small-text">• 重新校准判断</text>
  
  <rect x="980" y="220" width="200" height="80" class="data-box" rx="5"/>
  <text x="990" y="240" class="text">10. 校准结果</text>
  <text x="990" y="255" class="code-text">CalibrationResult</text>
  <text x="990" y="270" class="small-text">• 左眼映射矩阵</text>
  <text x="990" y="280" class="small-text">• 右眼映射矩阵</text>
  <text x="990" y="290" class="small-text">• 校准成功标志</text>
  
  <!-- 第四阶段：眼动追踪 -->
  <text x="50" y="340" class="subtitle">阶段4: 眼动追踪 (Gaze Tracking)</text>
  
  <rect x="50" y="350" width="200" height="100" class="step-box" rx="5"/>
  <text x="60" y="370" class="text">11. 开始追踪</text>
  <text x="60" y="385" class="code-text">TrackingManager.startTracking()</text>
  <text x="60" y="400" class="small-text">• 设置服务模式为TRACK</text>
  <text x="60" y="410" class="small-text">• 启动图像分析</text>
  <text x="60" y="420" class="small-text">• 初始化Native对象</text>
  <text x="60" y="430" class="small-text">• 设置回调监听</text>
  <text x="60" y="440" class="small-text">• 开始实时处理</text>
  
  <rect x="280" y="350" width="200" height="100" class="process-box" rx="5"/>
  <text x="290" y="370" class="text">12. JNI调用</text>
  <text x="290" y="385" class="code-text">GazeTrack.nativeGazeTracking()</text>
  <text x="290" y="400" class="small-text">• 传递ImageProxy数据</text>
  <text x="290" y="410" class="small-text">• 调用C++算法层</text>
  <text x="290" y="420" class="small-text">• 使用RKNN推理引擎</text>
  <text x="290" y="430" class="small-text">• 处理人脸和眼部特征</text>
  <text x="290" y="440" class="small-text">• 计算眼动坐标</text>
  
  <rect x="510" y="350" width="200" height="100" class="data-box" rx="5"/>
  <text x="520" y="370" class="text">13. 算法处理</text>
  <text x="520" y="385" class="code-text">GazeService.cpp</text>
  <text x="520" y="400" class="small-text">• 人脸检测算法</text>
  <text x="520" y="410" class="small-text">• 眼部关键点提取</text>
  <text x="520" y="420" class="small-text">• 视线方向计算</text>
  <text x="520" y="430" class="small-text">• 屏幕坐标映射</text>
  <text x="520" y="440" class="small-text">• 卡尔曼滤波平滑</text>
  
  <!-- 第五阶段：结果处理 -->
  <text x="750" y="340" class="subtitle">阶段5: 结果处理 (Result Processing)</text>
  
  <rect x="750" y="350" width="200" height="100" class="step-box" rx="5"/>
  <text x="760" y="370" class="text">14. 结果回调</text>
  <text x="760" y="385" class="code-text">onGazeTracking(result)</text>
  <text x="760" y="400" class="small-text">• 眼动坐标(x,y)</text>
  <text x="760" y="410" class="small-text">• 有效性标志</text>
  <text x="760" y="420" class="small-text">• 距离信息</text>
  <text x="760" y="430" class="small-text">• 姿势偏移检测</text>
  <text x="760" y="440" class="small-text">• 时间戳</text>
  
  <rect x="980" y="350" width="200" height="100" class="process-box" rx="5"/>
  <text x="990" y="370" class="text">15. UI更新</text>
  <text x="990" y="385" class="code-text">updateGazePoint(x, y)</text>
  <text x="990" y="400" class="small-text">• 实时眼动点显示</text>
  <text x="990" y="410" class="small-text">• WebSocket数据推送</text>
  <text x="990" y="420" class="small-text">• 悬浮窗口更新</text>
  <text x="990" y="430" class="small-text">• 数据记录和分析</text>
  <text x="990" y="440" class="small-text">• 异常状态处理</text>
  
  <!-- 连接线 -->
  <!-- 初始化阶段连接 -->
  <line x1="250" y1="130" x2="280" y2="130" class="arrow"/>
  <line x1="480" y1="130" x2="510" y2="130" class="arrow"/>
  
  <!-- 相机启动阶段连接 -->
  <line x1="250" y1="260" x2="280" y2="260" class="arrow"/>
  <line x1="480" y1="260" x2="510" y2="260" class="arrow"/>
  
  <!-- 校准阶段连接 -->
  <line x1="950" y1="130" x2="980" y2="130" class="arrow"/>
  <line x1="850" y1="170" x2="850" y2="220" class="arrow"/>
  <line x1="950" y1="260" x2="980" y2="260" class="success-arrow"/>
  
  <!-- 追踪阶段连接 -->
  <line x1="250" y1="400" x2="280" y2="400" class="arrow"/>
  <line x1="480" y1="400" x2="510" y2="400" class="arrow"/>
  <line x1="710" y1="400" x2="750" y2="400" class="arrow"/>
  <line x1="950" y1="400" x2="980" y2="400" class="arrow"/>
  
  <!-- 垂直连接 -->
  <line x1="150" y1="170" x2="150" y2="220" class="arrow"/>
  <line x1="380" y1="170" x2="380" y2="220" class="arrow"/>
  <line x1="610" y1="170" x2="610" y2="220" class="arrow"/>
  <line x1="610" y1="300" x2="610" y2="350" class="arrow"/>
  
  <!-- 错误处理流程 -->
  <text x="50" y="500" class="subtitle">错误处理流程 (Error Handling)</text>
  
  <rect x="50" y="510" width="180" height="60" class="decision-box" rx="5"/>
  <text x="60" y="530" class="text">权限被拒绝</text>
  <text x="60" y="545" class="small-text">• 显示权限说明</text>
  <text x="60" y="555" class="small-text">• 引导用户设置</text>
  <text x="60" y="565" class="small-text">• 重新请求权限</text>
  
  <rect x="250" y="510" width="180" height="60" class="decision-box" rx="5"/>
  <text x="260" y="530" class="text">相机启动失败</text>
  <text x="260" y="545" class="small-text">• 检查硬件支持</text>
  <text x="260" y="555" class="small-text">• 降级分辨率</text>
  <text x="260" y="565" class="small-text">• 重试机制</text>
  
  <rect x="450" y="510" width="180" height="60" class="decision-box" rx="5"/>
  <text x="460" y="530" class="text">校准失败</text>
  <text x="460" y="545" class="small-text">• 重新校准提示</text>
  <text x="460" y="555" class="small-text">• 调整环境光线</text>
  <text x="460" y="565" class="small-text">• 姿势指导</text>
  
  <rect x="650" y="510" width="180" height="60" class="decision-box" rx="5"/>
  <text x="660" y="530" class="text">追踪精度低</text>
  <text x="660" y="545" class="small-text">• 自动重新校准</text>
  <text x="660" y="555" class="small-text">• 环境适应调整</text>
  <text x="660" y="565" class="small-text">• 算法参数优化</text>
  
  <!-- 性能监控 -->
  <text x="900" y="500" class="subtitle">性能监控 (Performance)</text>
  
  <rect x="900" y="510" width="180" height="80" class="data-box" rx="5"/>
  <text x="910" y="530" class="text">实时性能指标</text>
  <text x="910" y="545" class="small-text">• 帧率: 30fps</text>
  <text x="910" y="555" class="small-text">• 延迟: &lt;33ms</text>
  <text x="910" y="565" class="small-text">• CPU使用率: &lt;20%</text>
  <text x="910" y="575" class="small-text">• 内存占用: &lt;100MB</text>
  <text x="910" y="585" class="small-text">• 精度: ±1度</text>
  
  <rect x="1100" y="510" width="180" height="80" class="data-box" rx="5"/>
  <text x="1110" y="530" class="text">质量保证</text>
  <text x="1110" y="545" class="small-text">• 自动质量检测</text>
  <text x="1110" y="555" class="small-text">• 异常状态恢复</text>
  <text x="1110" y="565" class="small-text">• 数据完整性校验</text>
  <text x="1110" y="575" class="small-text">• 实时状态监控</text>
  <text x="1110" y="585" class="small-text">• 日志记录分析</text>
  
  <!-- 错误处理连接线 -->
  <line x1="610" y1="170" x2="140" y2="510" class="error-arrow"/>
  <line x1="380" y1="300" x2="340" y2="510" class="error-arrow"/>
  <line x1="850" y1="300" x2="540" y2="510" class="error-arrow"/>
  <line x1="850" y1="450" x2="740" y2="510" class="error-arrow"/>
  
</svg>
