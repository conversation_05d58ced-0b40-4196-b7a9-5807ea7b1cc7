#include "face_det.h"

faceDetect::faceDetect()
{
    // ----- init rknn input buff -----
    data_inputs = (rknn_input*)malloc(sizeof(rknn_input));
    memset(&(data_inputs[0]), 0, sizeof(rknn_input));
    data_inputs[0].index = 0;
    data_inputs[0].pass_through = 0;
    data_inputs[0].type = RKNN_TENSOR_FLOAT32;
    data_inputs[0].fmt = RKNN_TENSOR_NHWC;
    data_inputs[0].size = face_det_net_w * face_det_net_h * get_type_size(RKNN_TENSOR_FLOAT32);

    // ---- init rknn output buff ----
    outputs = (rknn_output*)malloc(sizeof(rknn_output));
    memset(&(outputs[0]), 0, sizeof(rknn_output));
    outputs[0].want_float = 1;
}

faceDetect::~faceDetect()
{
    rknn_destroy(model_ctx);
    free(data_inputs);
    free(outputs);
}

void faceDetect::load_model(char* model_path, bool& model_isready)
{
    int ret = rknn_init(&model_ctx, (void *)model_path, 0, 0, NULL);

    if (ret < 0)
    {
        printf("rknn model of face det init fail! ret=%d\n", ret);
        model_isready = false;
    }
    else
    {
        printf("rknn model of face det init succeed! ret=%d\n", ret);
        model_isready = true;
    }
}


void faceDetect::recognize(const cv::Mat& src, cv::Rect& left_rect, cv::Rect& right_rect, bool& det_failed)
{
    // init det result
    det_failed = true;
    right_rect = cv::Rect(0, 0, 0, 0);
    left_rect = cv::Rect(0, 0, 0, 0);
    // ---------------
    int img_w = src.cols;
    int img_h = src.rows;

    LOGI("faceDetect::recognize - 开始人脸检测，输入图像尺寸: %dx%d", img_w, img_h);

    cv::Mat rs_image;
    cv::resize(src, rs_image, cv::Size(face_det_net_w, face_det_net_h));

    LOGI("faceDetect::recognize - 图像预处理完成，调整后尺寸: %dx%d, 通道数: %d",
         face_det_net_w, face_det_net_h, rs_image.channels());

    cout << "face det rs_image.channel = : " << rs_image.channels() << endl;
    // cv::Mat blob_image = cv::dnn::blobFromImage(rs_image, 1.0, cv::Size(face_det_net_w, face_det_net_h), cv::Scalar(), false, false, CV_32F);
    // 创建一个目标Mat对象用于存放转换后的数据，类型为CV_32FC1，即单通道float32
    cv::Mat blob_image;
    // 使用convertTo()函数进行类型转换
    rs_image.convertTo(blob_image, CV_32FC1); // 第二个参数是目标类型
    cout << "face det blob_image size = : " << blob_image.size<< endl;

    // --- input set ---
    auto *resize_buf = (unsigned char *)malloc(face_det_net_h* face_det_net_w * get_type_size(RKNN_TENSOR_FLOAT32));
    size_t dataSize = blob_image.total() * sizeof(float);
    memcpy(resize_buf, blob_image.data, dataSize);
    data_inputs[0].buf = resize_buf;
    int ret = 0;
    ret = rknn_inputs_set(model_ctx, 1, data_inputs);
    check_ret(ret, "face det rknn inputs set");

    // --- deploy ---
    ret = rknn_run(model_ctx, nullptr);
    check_ret(ret, "face det rknn run");

    // --- output ---
    ret = rknn_outputs_get(model_ctx, 1, outputs, nullptr);
    // 获取buff的指针，用于直接访问内存
    auto* buff_ptr = (float*)outputs[0].buf;
    check_ret(ret, "face det rknn outputs get");

    size_t output_size = outputs[0].size;
    LOGI("faceDetect::recognize - 模型推理完成，输出大小: %zu bytes", output_size);

    cv::Mat left_mask(face_det_net_h, face_det_net_w, CV_32F, buff_ptr); // 单通道float32
    cv::Mat right_mask(face_det_net_h, face_det_net_w, CV_32F, buff_ptr + face_det_net_h * face_det_net_w); // 同上

    LOGI("faceDetect::recognize - 开始查找最佳眼部位置，阈值: %.2f", face_det_threshold);
    LOGI("faceDetect::recognize - 左眼掩码尺寸: %dx%d, 类型: %d",
         left_mask.cols, left_mask.rows, left_mask.type());
    LOGI("faceDetect::recognize - 右眼掩码尺寸: %dx%d, 类型: %d",
         right_mask.cols, right_mask.rows, right_mask.type());

    // 计算掩码数值范围
    float left_min_val, left_max_val, right_min_val, right_max_val;
    get_mask_value_range(left_mask, left_min_val, left_max_val);
    get_mask_value_range(right_mask, right_min_val, right_max_val);

    LOGI("faceDetect::recognize - 左眼掩码数值范围: [%.6f, %.6f]", left_min_val, left_max_val);
    LOGI("faceDetect::recognize - 右眼掩码数值范围: [%.6f, %.6f]", right_min_val, right_max_val);

    // det result
    cv::Point left_eye_cent_ = cv::Point(-1, -1);
    cv::Point right_eye_cent_ = cv::Point(-1, -1);
    float left_best_value = 0.0;
    float right_best_value = 0.0;

    LOGI("faceDetect::recognize - 调用左眼检测，阈值: %.3f", face_det_threshold);
    find_best_point_by_prob_mask(left_mask, left_eye_cent_, left_best_value, face_det_net_w, face_det_net_h, face_det_threshold);

    LOGI("faceDetect::recognize - 调用右眼检测，阈值: %.3f", face_det_threshold);
    find_best_point_by_prob_mask(right_mask, right_eye_cent_, right_best_value, face_det_net_w, face_det_net_h, face_det_threshold);

    LOGI("faceDetect::recognize - 原始检测结果: 左眼(%d,%d,%.3f) 右眼(%d,%d,%.3f)",
         left_eye_cent_.x, left_eye_cent_.y, left_best_value,
         right_eye_cent_.x, right_eye_cent_.y, right_best_value);

    cv::Point left_eye_cent = cv::Point(-1, -1);
    cv::Point right_eye_cent = cv::Point(-1, -1);
    left_eye_cent.x = left_eye_cent_.x * img_w / face_det_net_w;
    left_eye_cent.y = left_eye_cent_.y * img_h / face_det_net_h;
    right_eye_cent.x = right_eye_cent_.x * img_w / face_det_net_w;
    right_eye_cent.y = right_eye_cent_.y * img_h / face_det_net_h;

    LOGI("faceDetect::recognize - 缩放后眼部中心: 左眼(%d,%d) 右眼(%d,%d)",
         left_eye_cent.x, left_eye_cent.y, right_eye_cent.x, right_eye_cent.y);

    double rect_width = 0;
    int width_distance = right_eye_cent.x - left_eye_cent.x;
    int height_distance = abs(right_eye_cent.y - left_eye_cent.y);

    LOGI("faceDetect::recognize - 眼部距离检查: 宽度距离=%d (范围:%d-%d), 高度距离=%d (最大:%d)",
         width_distance, eye_det_width_dist_min_threshold, eye_det_width_dist_max_threshold,
         height_distance, eye_det_height_dist_max_threshold);

    if (width_distance > eye_det_width_dist_min_threshold &&
        width_distance < eye_det_width_dist_max_threshold &&
        height_distance < eye_det_height_dist_max_threshold)
    {
        det_failed = false;
        rect_width = (double)abs(right_eye_cent.x - left_eye_cent.x) / 4.0 + expand_width;
        LOGI("faceDetect::recognize - 眼部检测成功，使用计算rect_width=%.2f", rect_width);
    }
    else
    {
        rect_width = (double)img_w * default_rect_ratio;
        LOGI("faceDetect::recognize - 眼部检测失败，使用默认rect_width=%.2f", rect_width);
    }

    double rect_height = rect_width * 0.75;

    LOGI("faceDetect::recognize - 最终检测结果: det_failed=%s", det_failed ? "true" : "false");

    // --- left rect ---
    if (left_eye_cent.x > 0 && left_eye_cent.y > 0)
    {
        // 判断中心点是否在原跟踪框内，如果是，则保持原跟踪框，否则更新跟踪框
        bool inside = left_rect.contains(left_eye_cent);
        if(!inside)
        {
            int left_rect_x0 = std::max(left_eye_cent.x - (int)rect_width, 0);
            int left_rect_x1 = std::min(left_eye_cent.x + (int)rect_width, img_w);
            int left_rect_y0 = std::max(left_eye_cent.y - (int)rect_height, 0);
            int left_rect_y1 = std::min(left_eye_cent.y + (int)rect_height, img_h);
            left_rect.x = left_rect_x0;
            left_rect.y = left_rect_y0;
            left_rect.width = left_rect_x1 - left_rect_x0;
            left_rect.height = left_rect_y1 - left_rect_y0;
        }
    }

    // --- right rect ---
    if (right_eye_cent.x > 0 && right_eye_cent.y > 0)
    {
        // 判断中心点是否在原跟踪框内，如果是，则保持原跟踪框，否则更新跟踪框
        bool inside = right_rect.contains(right_eye_cent);
        if(!inside)
        {
            int right_rect_x0 = std::max(right_eye_cent.x - (int)rect_width, 0);
            int right_rect_x1 = std::min(right_eye_cent.x + (int)rect_width, img_w);
            int right_rect_y0 = std::max(right_eye_cent.y - (int)rect_height, 0);
            int right_rect_y1 = std::min(right_eye_cent.y + (int)rect_height, img_h);
            right_rect.x = right_rect_x0;
            right_rect.y = right_rect_y0;
            right_rect.width = right_rect_x1 - right_rect_x0;
            right_rect.height = right_rect_y1 - right_rect_y0;
        }
    }

    LOGI("faceDetect::recognize - 左眼区域: [%d, %d, %d, %d]",
         left_rect.x, left_rect.y, left_rect.width, left_rect.height);
    LOGI("faceDetect::recognize - 右眼区域: [%d, %d, %d, %d]",
         right_rect.x, right_rect.y, right_rect.width, right_rect.height);

    // release input and output buffer
    ret = rknn_outputs_release(model_ctx, 1, outputs);
    check_ret(ret, "rknn outputs of face det release");
    delete[] resize_buf;
}