<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #e74c3c; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .mvvm-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .handler-box { fill: #fff3cd; stroke: #f39c12; stroke-width: 2; }
      .coroutine-box { fill: #e3f2fd; stroke: #3498db; stroke-width: 2; }
      .binding-box { fill: #f3e5f5; stroke: #9b59b6; stroke-width: 2; }
      .direct-box { fill: #ffeaa7; stroke: #fdcb6e; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">🎨 Android UI更新机制全景图</text>
  
  <!-- MVVM + LiveData 架构 -->
  <rect x="50" y="60" width="400" height="180" rx="15" class="mvvm-box"/>
  <text x="250" y="85" text-anchor="middle" class="subtitle">🏗️ MVVM + LiveData (主流方式)</text>
  <text x="60" y="110" class="text">📊 ViewModel处理业务逻辑:</text>
  <text x="60" y="130" class="code">viewModelScope.launch { userLiveData.postValue(data) }</text>
  <text x="60" y="150" class="text">👀 Activity/Fragment观察数据变化:</text>
  <text x="60" y="170" class="code">userVM.userLiveData.observe(this) { updateUI(it) }</text>
  <text x="60" y="190" class="text">✅ 优点: 生命周期安全、数据驱动、解耦</text>
  <text x="60" y="210" class="text">🎯 适用: 数据展示、状态管理、复杂业务逻辑</text>
  <text x="60" y="230" class="small-text">💡 示例: MainActivity更新设备信息、DetectionActivity更新用户头像</text>

  <!-- Handler机制 -->
  <rect x="480" y="60" width="400" height="180" rx="15" class="handler-box"/>
  <text x="680" y="85" text-anchor="middle" class="subtitle">⚡ Handler + Message (传统方式)</text>
  <text x="490" y="110" class="text">🔧 创建Handler:</text>
  <text x="490" y="130" class="code">LifecycleHandler(Looper.getMainLooper(), this)</text>
  <text x="490" y="150" class="text">📨 发送延时任务:</text>
  <text x="490" y="170" class="code">mHandler.postDelayed({ doSomething() }, 800)</text>
  <text x="490" y="190" class="text">✅ 优点: 精确控制、支持延时、消息队列</text>
  <text x="490" y="210" class="text">🎯 适用: 延时操作、定时任务、进程间通信</text>
  <text x="490" y="230" class="small-text">💡 示例: 扫码后延时跳转、GazeTrackService消息处理</text>

  <!-- 协程方式 -->
  <rect x="910" y="60" width="400" height="180" rx="15" class="coroutine-box"/>
  <text x="1110" y="85" text-anchor="middle" class="subtitle">🚀 协程 + Dispatchers (现代方式)</text>
  <text x="920" y="110" class="text">🔄 后台处理 + UI更新:</text>
  <text x="920" y="130" class="code">lifecycleScope.launch(Dispatchers.Default) {</text>
  <text x="920" y="150" class="code">  val result = heavyWork()</text>
  <text x="920" y="170" class="code">  withContext(Dispatchers.Main) { updateUI() }</text>
  <text x="920" y="190" class="text">✅ 优点: 结构化并发、自动取消、易读</text>
  <text x="920" y="210" class="text">🎯 适用: 异步操作、网络请求、文件IO</text>
  <text x="920" y="230" class="small-text">💡 示例: DeviceExceptionFragment网络状态检查</text>

  <!-- runOnUiThread -->
  <rect x="50" y="270" width="400" height="160" rx="15" class="handler-box"/>
  <text x="250" y="295" text-anchor="middle" class="subtitle">🏃 runOnUiThread (简单直接)</text>
  <text x="60" y="320" class="text">🔧 从子线程更新UI:</text>
  <text x="60" y="340" class="code">runOnUiThread {</text>
  <text x="60" y="360" class="code">  textView.text = "更新内容"</text>
  <text x="60" y="380" class="code">}</text>
  <text x="60" y="400" class="text">✅ 优点: 简单直接、无需Handler</text>
  <text x="60" y="420" class="small-text">💡 示例: HrvActivity中JavaScript回调处理</text>

  <!-- DataBinding -->
  <rect x="480" y="270" width="400" height="160" rx="15" class="binding-box"/>
  <text x="680" y="295" text-anchor="middle" class="subtitle">🔗 ViewBinding (视图绑定)</text>
  <text x="490" y="320" class="text">📱 直接访问视图控件:</text>
  <text x="490" y="340" class="code">binding.tvCopyright.text = deviceInfo?.copyright</text>
  <text x="490" y="360" class="code">binding.ivLogo.setImageResource(R.drawable.logo)</text>
  <text x="490" y="380" class="text">✅ 优点: 类型安全、空安全、性能好</text>
  <text x="490" y="400" class="text">🎯 适用: 直接UI操作、简单数据绑定</text>
  <text x="490" y="420" class="small-text">💡 示例: MainActivity中设备信息显示</text>

  <!-- LiveEventBus -->
  <rect x="910" y="270" width="400" height="160" rx="15" class="coroutine-box"/>
  <text x="1110" y="295" text-anchor="middle" class="subtitle">📡 LiveEventBus (跨进程通信)</text>
  <text x="920" y="320" class="text">🌐 跨进程事件传递:</text>
  <text x="920" y="340" class="code">LiveEventBus.get&lt;Boolean&gt;(EVENT_NAME)</text>
  <text x="920" y="360" class="code">  .postAcrossProcess(true)</text>
  <text x="920" y="380" class="text">✅ 优点: 跨进程、解耦、事件驱动</text>
  <text x="920" y="400" class="text">🎯 适用: 应用状态变化、跨组件通信</text>
  <text x="920" y="420" class="small-text">💡 示例: 应用前后台状态变化通知</text>

  <!-- 直接UI操作 -->
  <rect x="50" y="460" width="400" height="160" rx="15" class="direct-box"/>
  <text x="250" y="485" text-anchor="middle" class="subtitle">🎯 直接UI操作 (即时更新)</text>
  <text x="60" y="510" class="text">⚡ 直接调用View方法:</text>
  <text x="60" y="530" class="code">progressBar.progress = (progress * 100).toInt()</text>
  <text x="60" y="550" class="code">Toast.makeText(this, message, LENGTH_SHORT).show()</text>
  <text x="60" y="570" class="text">✅ 优点: 即时响应、简单直接</text>
  <text x="60" y="590" class="text">🎯 适用: 进度更新、错误提示、简单状态变化</text>
  <text x="60" y="610" class="small-text">💡 示例: UpdateActivity进度条更新、错误Toast显示</text>

  <!-- 使用场景对比 -->
  <rect x="480" y="460" width="830" height="200" rx="15" class="mvvm-box"/>
  <text x="895" y="485" text-anchor="middle" class="subtitle">📋 使用场景对比与选择指南</text>
  
  <text x="490" y="510" class="text">🏗️ <tspan class="subtitle">复杂业务逻辑</tspan> → MVVM + LiveData</text>
  <text x="490" y="530" class="small-text">   • 用户信息管理、设备状态管理、数据列表展示</text>
  
  <text x="490" y="550" class="text">⏰ <tspan class="subtitle">延时和定时任务</tspan> → Handler</text>
  <text x="490" y="570" class="small-text">   • 扫码后延时跳转、定时刷新、消息队列处理</text>
  
  <text x="490" y="590" class="text">🔄 <tspan class="subtitle">异步操作</tspan> → 协程 + Dispatchers</text>
  <text x="490" y="610" class="small-text">   • 网络请求、文件操作、重计算任务</text>
  
  <text x="490" y="630" class="text">⚡ <tspan class="subtitle">即时UI更新</tspan> → 直接操作 / runOnUiThread</text>
  <text x="490" y="650" class="small-text">   • 进度条更新、Toast提示、简单状态切换</text>

  <!-- 架构演进 -->
  <rect x="50" y="680" width="1260" height="120" rx="15" class="coroutine-box"/>
  <text x="680" y="705" text-anchor="middle" class="subtitle">🔄 架构演进趋势</text>
  
  <!-- 演进箭头 -->
  <line x1="100" y1="740" x2="200" y2="740" class="arrow"/>
  <line x1="250" y1="740" x2="350" y2="740" class="arrow"/>
  <line x1="400" y1="740" x2="500" y2="740" class="arrow"/>
  <line x1="550" y1="740" x2="650" y2="740" class="arrow"/>
  <line x1="700" y1="740" x2="800" y2="740" class="arrow"/>
  <line x1="850" y1="740" x2="950" y2="740" class="arrow"/>
  <line x1="1000" y1="740" x2="1100" y2="740" class="arrow"/>
  
  <text x="70" y="735" class="text">Handler</text>
  <text x="150" y="755" class="small-text">传统方式</text>
  
  <text x="220" y="735" class="text">runOnUiThread</text>
  <text x="225" y="755" class="small-text">简化操作</text>
  
  <text x="370" y="735" class="text">LiveData</text>
  <text x="375" y="755" class="small-text">响应式</text>
  
  <text x="520" y="735" class="text">ViewModel</text>
  <text x="525" y="755" class="small-text">MVVM架构</text>
  
  <text x="670" y="735" class="text">协程</text>
  <text x="665" y="755" class="small-text">现代异步</text>
  
  <text x="820" y="735" class="text">ViewBinding</text>
  <text x="825" y="755" class="small-text">类型安全</text>
  
  <text x="1020" y="735" class="text">Compose</text>
  <text x="1015" y="755" class="small-text">声明式UI</text>
  
  <text x="1150" y="735" class="text">未来</text>
  <text x="1145" y="755" class="small-text">更多创新</text>

  <!-- 最佳实践建议 -->
  <rect x="50" y="820" width="1260" height="160" rx="15" class="mvvm-box"/>
  <text x="680" y="845" text-anchor="middle" class="subtitle">💡 最佳实践建议</text>
  
  <text x="60" y="870" class="text">🎯 <tspan class="subtitle">新项目推荐</tspan>: MVVM + LiveData + 协程 + ViewBinding</text>
  <text x="60" y="890" class="text">🔧 <tspan class="subtitle">维护项目</tspan>: 渐进式重构，保持现有Handler机制稳定运行</text>
  <text x="60" y="910" class="text">⚡ <tspan class="subtitle">性能优化</tspan>: 避免在主线程执行重操作，合理使用缓存</text>
  <text x="60" y="930" class="text">🛡️ <tspan class="subtitle">内存安全</tspan>: 使用LifecycleHandler防止内存泄漏</text>
  <text x="60" y="950" class="text">🧪 <tspan class="subtitle">测试友好</tspan>: ViewModel便于单元测试，LiveData支持测试观察</text>
  <text x="60" y="970" class="small-text">💼 当前项目特点: 传统与现代并存，在特定场景下灵活选择最适合的方案</text>

</svg>
