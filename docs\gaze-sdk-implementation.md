# 眼动追踪SDK具体实现方案

## 1. 创建AAR模块

### 1.1 新建Library模块
```bash
# 在项目根目录创建新模块
mkdir gaze-tracking-sdk
cd gaze-tracking-sdk
```

### 1.2 模块build.gradle.kts
```kotlin
plugins {
    id("com.android.library")
    id("kotlin-android")
    id("kotlin-parcelize")
    id("kotlin-kapt")
    id("maven-publish")
}

android {
    namespace = "com.airdoc.gaze"
    compileSdk = 34

    defaultConfig {
        minSdk = 29
        targetSdk = 34
        
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")

        externalNativeBuild {
            cmake {
                cppFlags("-std=c++14", "-stdlib=libc++")
                arguments("-DANDROID_STL=c++_shared")
                arguments("-DANDROID_TOOLCHAIN=clang")
                abiFilters("arm64-v8a")
            }
        }

        ndk {
            abiFilters.add("arm64-v8a")
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }

    buildFeatures {
        viewBinding = true
        buildConfig = true
    }
}

dependencies {
    // AndroidX核心库
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-service:2.7.0")
    
    // CameraX
    implementation("androidx.camera:camera-core:1.3.1")
    implementation("androidx.camera:camera-camera2:1.3.1")
    implementation("androidx.camera:camera-lifecycle:1.3.1")
    
    // 协程
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
    
    // JSON解析
    implementation("com.google.code.gson:gson:2.10.1")
    
    // WebSocket
    implementation("org.java-websocket:Java-WebSocket:1.5.3")
    
    // 日志
    implementation("com.airdoc.component:common:1.0.0")
}

// Maven发布配置
publishing {
    publications {
        create<MavenPublication>("release") {
            from(components["release"])
            
            groupId = "com.airdoc"
            artifactId = "gaze-tracking-sdk"
            version = "1.0.0"
            
            pom {
                name.set("Gaze Tracking SDK")
                description.set("Eye tracking SDK for Android")
                url.set("https://github.com/airdoc/gaze-tracking-sdk")
                
                licenses {
                    license {
                        name.set("The Apache License, Version 2.0")
                        url.set("http://www.apache.org/licenses/LICENSE-2.0.txt")
                    }
                }
                
                developers {
                    developer {
                        id.set("airdoc")
                        name.set("AirDoc Team")
                        email.set("<EMAIL>")
                    }
                }
            }
        }
    }
}
```

## 2. 核心API设计

### 2.1 主SDK类
```kotlin
// src/main/java/com/airdoc/gaze/api/GazeTrackingSDK.kt
package com.airdoc.gaze.api

import android.app.Activity
import android.content.Context
import com.airdoc.gaze.core.GazeTrackingManager
import com.airdoc.gaze.model.GazeConfig
import com.airdoc.gaze.model.enums.CalibrationMode

class GazeTrackingSDK private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: GazeTrackingSDK? = null
        
        fun getInstance(): GazeTrackingSDK {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: GazeTrackingSDK().also { INSTANCE = it }
            }
        }
    }
    
    private var isInitialized = false
    private lateinit var gazeManager: GazeTrackingManager
    
    /**
     * 初始化SDK
     */
    fun init(context: Context, config: GazeConfig): Boolean {
        return try {
            gazeManager = GazeTrackingManager.getInstance()
            gazeManager.init(context, config)
            isInitialized = true
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 开始眼动追踪
     */
    fun startTracking(listener: IGazeTrackListener): Boolean {
        if (!isInitialized) return false
        return gazeManager.startTracking(listener)
    }
    
    /**
     * 停止眼动追踪
     */
    fun stopTracking(): Boolean {
        if (!isInitialized) return false
        return gazeManager.stopTracking()
    }
    
    /**
     * 开始校准
     */
    fun startCalibration(
        activity: Activity, 
        mode: CalibrationMode,
        listener: ICalibrationListener
    ): Boolean {
        if (!isInitialized) return false
        return gazeManager.startCalibration(activity, mode, listener)
    }
    
    /**
     * 检查是否正在追踪
     */
    fun isTracking(): Boolean {
        return if (isInitialized) gazeManager.isTracking() else false
    }
    
    /**
     * 释放资源
     */
    fun release() {
        if (isInitialized) {
            gazeManager.release()
            isInitialized = false
        }
    }
}
```

### 2.2 监听器接口
```kotlin
// src/main/java/com/airdoc/gaze/api/IGazeTrackListener.kt
package com.airdoc.gaze.api

import com.airdoc.gaze.model.GazeTrackResult
import com.airdoc.gaze.model.GazeError
import com.airdoc.gaze.model.enums.ServiceMode

interface IGazeTrackListener {
    /**
     * 眼动追踪结果回调
     */
    fun onGazeTracking(result: GazeTrackResult)
    
    /**
     * 服务模式变化回调
     */
    fun onServiceModeChange(mode: ServiceMode) {}
    
    /**
     * 错误回调
     */
    fun onError(error: GazeError) {}
}

// src/main/java/com/airdoc/gaze/api/ICalibrationListener.kt
package com.airdoc.gaze.api

import com.airdoc.gaze.model.CalibrationResult

interface ICalibrationListener {
    /**
     * 校准完成回调
     */
    fun onCalibrationComplete(result: CalibrationResult)
    
    /**
     * 校准进度回调
     */
    fun onCalibrationProgress(progress: Int) {}
    
    /**
     * 校准错误回调
     */
    fun onCalibrationError(error: String) {}
}
```

### 2.3 配置类
```kotlin
// src/main/java/com/airdoc/gaze/model/GazeConfig.kt
package com.airdoc.gaze.model

data class GazeConfig(
    val modelPath: String,
    val configPath: String,
    val enableWebSocket: Boolean = true,
    val webSocketPort: Int = 8080,
    val enableDebugLog: Boolean = false,
    val cameraResolution: String = "4048x3040"
) {
    class Builder {
        private var modelPath: String = "models/"
        private var configPath: String = "config/"
        private var enableWebSocket: Boolean = true
        private var webSocketPort: Int = 8080
        private var enableDebugLog: Boolean = false
        private var cameraResolution: String = "4048x3040"
        
        fun setModelPath(path: String) = apply { this.modelPath = path }
        fun setConfigPath(path: String) = apply { this.configPath = path }
        fun setEnableWebSocket(enable: Boolean) = apply { this.enableWebSocket = enable }
        fun setWebSocketPort(port: Int) = apply { this.webSocketPort = port }
        fun setEnableDebugLog(enable: Boolean) = apply { this.enableDebugLog = enable }
        fun setCameraResolution(resolution: String) = apply { this.cameraResolution = resolution }
        
        fun build() = GazeConfig(
            modelPath, configPath, enableWebSocket, 
            webSocketPort, enableDebugLog, cameraResolution
        )
    }
}
```

## 3. 文件迁移清单

### 3.1 需要迁移的核心文件
```
从 app/src/main/java/com/airdoc/mpd/gaze/ 迁移到 gaze-tracking-sdk/src/main/java/com/airdoc/gaze/

核心服务:
- track/GazeTrackService.kt → core/GazeTrackService.kt
- track/TrackingManager.kt → core/TrackingManager.kt
- GazeTrackingManager.kt → core/GazeTrackingManager.kt

JNI层:
- track/GazeTrack.kt → jni/GazeTrack.kt

相机管理:
- camera/GTCameraManager.kt → camera/GTCameraManager.kt
- camera/ICameraListener.kt → camera/ICameraListener.kt

校准系统:
- calibration/CalibrationActivity.kt → calibration/CalibrationActivity.kt
- widget/PostureCalibrationView.kt → calibration/PostureCalibrationView.kt
- widget/VisualCalibrationView.kt → calibration/VisualCalibrationView.kt

数据模型:
- bean/ → model/
- enumeration/ → model/enums/
- listener/ → api/

工具类:
- utils/ → utils/
```

### 3.2 需要迁移的Native文件
```
从 app/src/main/cpp/ 迁移到 gaze-tracking-sdk/src/main/cpp/

- native-lib.cpp
- GazeService.cpp/h
- GazeApplication.cpp/h
- 所有算法相关的cpp/h文件
- include/ 目录下的第三方库头文件
- CMakeLists.txt (需要修改)
```

### 3.3 需要迁移的资源文件
```
从 app/src/main/ 迁移到 gaze-tracking-sdk/src/main/

- assets/models/ → assets/models/
- assets/config/ → assets/config/
- res/layout/activity_calibration.xml → res/layout/
- res/values/strings.xml (相关部分) → res/values/
```

## 4. 构建和发布

### 4.1 构建AAR
```bash
# 构建release版本
./gradlew :gaze-tracking-sdk:assembleRelease

# 生成的AAR文件位置
# gaze-tracking-sdk/build/outputs/aar/gaze-tracking-sdk-release.aar
```

### 4.2 发布到Maven仓库
```bash
# 发布到本地Maven仓库
./gradlew :gaze-tracking-sdk:publishToMavenLocal

# 发布到远程Maven仓库
./gradlew :gaze-tracking-sdk:publish
```

## 5. 使用示例

### 5.1 在项目中集成
```kotlin
// app/build.gradle.kts
dependencies {
    implementation("com.airdoc:gaze-tracking-sdk:1.0.0")
}
```

### 5.2 使用示例
```kotlin
class MainActivity : AppCompatActivity() {
    private val gazeSDK = GazeTrackingSDK.getInstance()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化SDK
        val config = GazeConfig.Builder()
            .setModelPath("models/")
            .setConfigPath("config/")
            .setEnableWebSocket(true)
            .build()
            
        if (gazeSDK.init(this, config)) {
            // 开始追踪
            gazeSDK.startTracking(object : IGazeTrackListener {
                override fun onGazeTracking(result: GazeTrackResult) {
                    runOnUiThread {
                        // 更新UI显示眼动点
                        updateGazePoint(result.x, result.y)
                    }
                }
                
                override fun onError(error: GazeError) {
                    Log.e("Gaze", "Error: ${error.message}")
                }
            })
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        gazeSDK.release()
    }
}
```

这个方案提供了完整的AAR封装架构，包括模块结构、API设计、文件迁移清单和使用示例。
