//
// Created by user on 2024/12/16.
//

#include "poseAlign.h"

PoseAlign::PoseAlign()
{
    pose_preview_eye_points = pose_pupil_points();
    setting_refer_points = pose_pupil_points(cv::Point2f(setting_pose_left_eye_x, setting_pose_left_eye_y), cv::Point2f(setting_pose_right_eye_x, setting_pose_right_eye_y));
    record_time = Clock::now();
    pose_align_time = Clock::now();

    expect_interpupillary_distance = -1.0F;

    cunt_down_second = cunt_down_second_pose_align;
    remaining = cunt_down_second_pose_align;

    finish_flag = false;
    ignoring = true;
}

PoseAlign::~PoseAlign() = default;

// 开启姿势校准&初始化
void PoseAlign::start_aligning_head_pose()
{
    pose_preview_eye_points = pose_pupil_points();
    cunt_down_second = cunt_down_second_pose_align;
    remaining = cunt_down_second_pose_align;
    // record_time = Clock::now();
    pose_align_time = Clock::now();

    expect_interpupillary_distance = -1.0F;

    finish_flag = false;
    ignoring = false;
}

// 关闭姿势校准
void PoseAlign::stop_aligning_head_pose()
{
    if(!finish_flag)
    {
        record_time = Clock::now();
        finish_flag = true;
        ignoring = true;
    }
}

// 跟踪过程中的姿势评估函数
bool PoseAlign::recognize_pose_valid(const frame_detection_result& det_result,bool isTracking)
{
    bool pose_is_skew = false;    // 初始化状态，姿势未发生了偏移
    LOGI("------- GazeCalibrate::recognize_pose_valid : ignoring = %s ------", ignoring ? "true" : "false");
    if(ignoring)
    {
        // 在忽略偏移的时间间隔内
        pose_is_skew = false;
        auto click_now = Clock::now();  // time Now
        double keep_time = (double) std::chrono::duration_cast<std::chrono::nanoseconds>(click_now - record_time).count() / 1e+6;  // 姿势偏移的容忍时间 ms
        if(keep_time > pose_deviate_notify_min_time)
        {
            // 容忍时间超过限定期限
            ignoring = false;
        }
    }
    else
    {
        bool det_succeed = det_result.left_eye.valid & det_result.right_eye.valid;
        LOGI("------- GazeCalibrate::recognize_pose_valid : det_succeed = %s ------", det_succeed ? "true" : "false");
        if (det_succeed) {
            float eye_left_x = det_result.left_eye.pupil.x * (float) screen_image_width_ratio;
            float eye_left_y = det_result.left_eye.pupil.y * (float) screen_image_height_ratio;
            float eye_right_x = det_result.right_eye.pupil.x * (float) screen_image_width_ratio;
            float eye_right_y = det_result.right_eye.pupil.y * (float) screen_image_height_ratio;

            float left_distance = sqrtf(powf(eye_left_x - setting_refer_points.left_pupil.x, 2) +
                                        powf(eye_left_y - setting_refer_points.left_pupil.y, 2));
            float right_distance = sqrtf(powf(eye_right_x - setting_refer_points.right_pupil.x, 2) +
                                         powf(eye_right_y - setting_refer_points.right_pupil.y, 2));

            float dist_ratio = -1.0F;
            if(expect_interpupillary_distance > 0)
            {
                // 完成对瞳距的校准
                dist_ratio = det_result.interpupillary_distance / expect_interpupillary_distance;
            }
            else
            {
                auto mean_iris_width = (float)(det_result.left_eye.iris_rect.width + det_result.right_eye.iris_rect.width) / 2.0F;
                dist_ratio = mean_iris_width / setting_pose_iris_width;
            }
            LOGI("------- GazeCalibrate::recognize_pose_valid : right_distance = %1f --- left_distance = %2f --- dist_ratio = %3f ---", right_distance,left_distance,dist_ratio);
            if (right_distance < pose_align_eye_radius && left_distance < pose_align_eye_radius &&
                dist_ratio > pose_align_iris_width_ratio_min_rate && dist_ratio < pose_align_iris_width_ratio_max_rate) {
                // 双眼在限定范围内，并且瞳距符合要求
                pose_is_skew = false;
                record_time = Clock::now();   // 更新记录时间
            } else{
                pose_is_skew = true;
            }
        } else{
            pose_is_skew = true;
        }

        if(pose_is_skew && isTracking)
        {
            // 如果姿势发生了偏移，查看是否在容忍范围内
            auto click_now = Clock::now();  // time Now
            double keep_time = (double) std::chrono::duration_cast<std::chrono::nanoseconds>(click_now - record_time).count() / 1e+6;  // 姿势偏移的容忍时间 ms
            LOGI("------- GazeCalibrate::recognize_pose_valid : keep_time = %f ---", keep_time);
            if(keep_time < det_failed_tolerate_duration)
            {
                pose_is_skew = false;     // 姿势偏移在容忍时间内
            }
        }
    }
    return pose_is_skew;
}

pose_align_result PoseAlign::head_pose_aligning(const frame_detection_result& det_result)
{
    pose_align_result pose_result;
    LOGI("------- GazeCalibrate::head_pose_aligning : finish_flag = %s ------", finish_flag ? "true" : "false");
    if(!finish_flag)
    {
        // --- 计算50cm距离的最佳瞳距的像素宽度 ---
        // 两眼的虹膜直径比例
        float two_iris_width_ratio = (float)det_result.left_eye.iris_rect.width / (float)det_result.right_eye.iris_rect.width;
        LOGI("------- GazeCalibrate::head_pose_align : two_iris_width_ratio = %f ------", two_iris_width_ratio);
        if (two_iris_width_ratio >= two_eye_compare_iris_width_ratio_min && two_iris_width_ratio < two_eye_compare_iris_width_ratio_max)
        {
            auto mean_iris_width = (float)(det_result.left_eye.iris_rect.width + det_result.right_eye.iris_rect.width) / 2.0F;
            float tmp_rate = mean_iris_width / setting_pose_iris_width;
            float tmp_norm_dist = det_result.interpupillary_distance / tmp_rate;
            if(expect_interpupillary_distance > 0)
            {
                expect_interpupillary_distance = expect_interpupillary_distance * 0.8F + tmp_norm_dist * 0.2F;
            }
            else
            {
                expect_interpupillary_distance = tmp_norm_dist;
            }
        }

        // 判断是否在限定范围内
        bool skew = recognize_pose_valid(det_result, false);
        pose_result.aligning = !skew;
        LOGI("------- GazeCalibrate::head_pose_aligning : pose_result.aligning = %s ------", pose_result.aligning ? "true" : "false");
        pose_pupil_points cur_p;
        cur_p = det_result;     //取det_result的瞳孔坐标
        float diff = cur_p - pose_preview_eye_points;
        LOGI("------- GazeCalibrate::head_pose_aligning : diff = %f ------", diff);
        if(pose_result.aligning && diff < pose_stable_move_min)
        {
            // 人眼在限定范围内
            auto click_now = Clock::now();  // click nows
            double keep_time = (double) std::chrono::duration_cast<std::chrono::nanoseconds>(
                    click_now - pose_align_time).count() / 1e+9;
            remaining = max(0, cunt_down_second - (int) keep_time);  // 倒计时

            LOGI("------- GazeCalibrate::head_pose_aligning : remaining = %d ------", remaining);
            if (remaining == 0)
            {
                // 倒计时完成
                pose_result.pose_aligned = true;
                finish_flag = true;
            }
        }
        else
        {
            // 获取当前时间点
            pose_align_time = Clock::now();
            remaining = cunt_down_second;
        }
        pose_preview_eye_points = cur_p;
        pose_result.countdown = remaining;      // 倒计时
        double norm_dist_ratio = expect_interpupillary_distance / det_result.interpupillary_distance;
        pose_result.dist = (float) distance_sigmoid(norm_dist_ratio);

        // 双眼的位置坐标
        pose_result.left_pupil = Point2f(cur_p.left_pupil.x / (float)image_width, cur_p.left_pupil.y / (float)image_height);
        pose_result.right_pupil = Point2f(cur_p.right_pupil.x / (float)image_width, cur_p.right_pupil.y / (float)image_height);
    }
    else
    {
        pose_result.pose_aligned = true;
    }

    return pose_result;
}

Mat PoseAlign::draw_poseAlign_func(cv::Mat &bgr_img, const pose_align_result& pose_result)
{
    cv::Scalar show_color;
    if (pose_result.aligning)
    {
        show_color = cv::Scalar(0, 255, 0);
    }
    else
    {
        show_color = cv::Scalar(0, 0, 255);
    }

    if (pose_result.left_pupil.x > 0 && pose_result.left_pupil.y > 0 && pose_result.right_pupil.x > 0 && pose_result.right_pupil.y > 0) {

        circle(bgr_img, Point(setting_refer_points.left_pupil), pose_align_eye_radius,
               Scalar(0, 0, 255), 5);  // left eye
        circle(bgr_img, Point(setting_refer_points.right_pupil), pose_align_eye_radius,
               Scalar(0, 255, 0), 5);  // right eye
        // 使用 line 函数绘制直线
        cv::line(bgr_img, cv::Point(960, 0), cv::Point(960, 1080), cv::Scalar(125, 125, 125), 3);  // (image, pt1, pt2, color, thickness)

        circle(bgr_img, Point(pose_result.left_pupil.x * visual_image_width, pose_result.left_pupil.y * visual_image_height), 7, show_color,
               -1);//left eye
        circle(bgr_img, Point(pose_result.right_pupil.x * visual_image_width, pose_result.right_pupil.y * visual_image_height), 7, show_color,
               -1);//right eye
    }

    char text[256];
    snprintf(text, 256, "%d", pose_result.countdown);
    cv::putText(bgr_img, text, cv::Point(900, 600), cv::FONT_HERSHEY_SIMPLEX, 10,
                show_color, 5);

    char text2[256];
    snprintf(text2, 256, "%f", pose_result.dist);
    cv::putText(bgr_img, text2, cv::Point(100, 50), cv::FONT_HERSHEY_SIMPLEX, 2,
                show_color, 2);

    return bgr_img;
}


