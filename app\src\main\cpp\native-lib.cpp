#include <jni.h>
#include <string>
#include <unistd.h>
#include "GazeService.h"
#include "GazeApplication.h"
#include "uploadCloud.h"

bool finish_calib = false;
JavaVM* gJvm;
/**
 * 初始化Java虚拟机的引用
 */
extern "C" JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
    gJvm = vm;
    return JNI_VERSION_1_6;
}

/**
 * 设置眼动追踪回调
 */
extern "C"
JNIEXPORT void JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeSetGazeTrackCallback(JNIEnv* env, jclass obj, jlong thiz, jobject callback_calib) {
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        tracker_service->setGazeTrackCallback(env, callback_calib);
    }
}

/**
 * 初始化视线追踪
 */
extern "C"
JNIEXPORT jlong JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeCreateObject(JNIEnv *env, jclass clazz, jstring config_dir, jstring sn_serial) {
    const char *config_dir_ = env->GetStringUTFChars(config_dir, nullptr);
    const char *sn_serial_ = env->GetStringUTFChars(sn_serial, nullptr);

    LOGI("nativeCreateObject - 开始创建GazeService");
    LOGI("nativeCreateObject - config_dir: %s", config_dir_);
    LOGI("nativeCreateObject - sn_serial: %s", sn_serial_);

    auto *tracker_service = new GazeService(config_dir_, sn_serial_);

    if (tracker_service != nullptr) {
        LOGI("nativeCreateObject - GazeService创建成功，地址: %p", tracker_service);
    } else {
        LOGE("nativeCreateObject - GazeService创建失败");
    }

    env->ReleaseStringUTFChars(config_dir, config_dir_);
    env->ReleaseStringUTFChars(sn_serial, sn_serial_);

    // 返回tracker_service地址给Java层
    return (jlong) tracker_service;
}

// ****************************** tracker-start ***********************************
/**
 * 启动视线追踪
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeStartTracking(JNIEnv *env, jclass clazz, jlong thiz) {
    LOGI("nativeStartTracking - 开始启动视线追踪，handle: %ld", thiz);

    bool flag = false;
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        LOGI("nativeStartTracking - GazeService地址: %p", tracker_service);

        flag = tracker_service->start_tracking();

        LOGI("nativeStartTracking - 启动结果: %s", flag ? "成功" : "失败");
        

    } else {
        LOGE("nativeStartTracking - 无效的handle");
    }
    return flag;
}

/**
 * 停止视线追踪
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeStopTracking(JNIEnv *env, jclass clazz, jlong thiz) {
    bool flag = false;
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        flag = tracker_service->stop_tracking();
    }
    return flag;
}

/**
 * 读取是否成功加载校准参数
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeGetCalibState(JNIEnv *env, jclass clazz, jlong thiz) {
    LOGI("nativeGetCalibState - 检查校准参数状态，handle: %ld", thiz);

    if (thiz == 0) {
        LOGE("nativeGetCalibState - 无效的handle");
        return false;
    }
    auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
    LOGI("nativeGetCalibState - GazeService地址: %p", tracker_service);

    jboolean calib_flag = tracker_service->get_tracker_state_func(); // 调用C++函数获取结果

    LOGI("nativeGetCalibState - 校准参数状态: %s", calib_flag ? "已加载" : "未加载");

    return calib_flag;
}

/**
 * 刷新跟踪器的眼动参数。从本地文件读取参数信息
 */
extern "C"
JNIEXPORT void JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeUpdateGazeParams(JNIEnv *env, jclass clazz, jlong thiz) {
    LOGI("nativeUpdateGazeParams - 开始更新眼动参数，handle: %ld", thiz);

    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        LOGI("nativeUpdateGazeParams - GazeService地址: %p", tracker_service);

        // 加载眼动校准参数
        tracker_service->update_tracker_config_func();

        LOGI("nativeUpdateGazeParams - 眼动参数更新完成");
    } else {
        LOGE("nativeUpdateGazeParams - 无效的handle");
    }
}

/**
 * 眼动服务：视线追踪处理函数
 */
extern "C"
JNIEXPORT jobject JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeGazeTracking(JNIEnv *env, jclass clazz, jlong thiz,
                                                    jbyteArray inputImage_, jint width, jint height) {
    LOGI("nativeGazeTracking - 开始处理图像，handle: %ld, 图像尺寸: %dx%d", thiz, width, height);

    jclass hashMapClass = env->FindClass("java/util/HashMap");
    jmethodID hashMapConstructor = env->GetMethodID(hashMapClass, "<init>", "()V");
    jmethodID hashMapPut = env->GetMethodID(hashMapClass, "put", "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;");

    jobject hashMap = env->NewObject(hashMapClass, hashMapConstructor);
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        LOGI("nativeGazeTracking - GazeService地址: %p", tracker_service);

        // 将图片数据转化为jbyte
        jbyte *inputImage = env->GetByteArrayElements(inputImage_, nullptr);
        if (inputImage == nullptr) {
            LOGE("nativeGazeTracking - 获取图像数据失败");
            return hashMap;
        }

        // 获取图像数据大小
        jsize imageSize = env->GetArrayLength(inputImage_);
        LOGI("nativeGazeTracking - 图像数据大小: %d bytes, 预期大小: %d bytes", imageSize, width * height);

        // 根据I420宽高，设置Mat（OpenCV支持的图片格式）的宽高，并赋值 src
        Mat grayimg(height, width, CV_8UC1, (unsigned char*)inputImage);
        LOGI("nativeGazeTracking - OpenCV Mat创建完成，尺寸: %dx%d, 通道数: %d", grayimg.cols, grayimg.rows, grayimg.channels());

        // 视线追踪
        LOGI("nativeGazeTracking - 开始调用process_gazetracker");
        gazetracker_result_str result = tracker_service->process_gazetracker(grayimg);
        LOGI("nativeGazeTracking - process_gazetracker完成，结果: valid=%s, x=%.3f, y=%.3f, dist=%.1f",
             result.valid ? "true" : "false", result.gaze_x, result.gaze_y, result.dist);

        Mat visual_img = tracker_service->visual_tracker_result_func(grayimg);
        Mat rgba_visual; // BGR image
        cvtColor(visual_img, rgba_visual, COLOR_BGR2RGBA);
//        tracker_service->draw(rgba_visual);

        jboolean jboolResult_valid = result.valid;
        jboolean jboolResult_skew = result.skew;
        jfloat jfloatResult_x = result.gaze_x;
        jfloat jfloatResult_y = result.gaze_y;
        jfloat jfloatResult_dist = result.dist;
        jfloat jfloatResult_duration = result.duration;

        auto jboolValue_valid = static_cast<jboolean>(jboolResult_valid);
        auto jboolValue_skew = static_cast<jboolean>(jboolResult_skew);
        auto jfloatValue_x = static_cast<jfloat>(jfloatResult_x);
        auto jfloatValue_y = static_cast<jfloat>(jfloatResult_y);
        auto jfloatValue_dist = static_cast<jfloat>(jfloatResult_dist);
        auto jfloatValue_duration = static_cast<jfloat>(jfloatResult_duration);

        // 将 jfloat 转换为 Float 对象
        jclass floatClass = env->FindClass("java/lang/Float");
        jmethodID floatConstructor = env->GetMethodID(floatClass, "<init>", "(F)V");
        jobject jfloatObject_x = env->NewObject(floatClass, floatConstructor, jfloatValue_x);
        jobject jfloatObject_y = env->NewObject(floatClass, floatConstructor, jfloatValue_y);
        jobject jfloatObject_dist = env->NewObject(floatClass, floatConstructor, jfloatValue_dist);
        jobject jfloatObject_duration = env->NewObject(floatClass, floatConstructor, jfloatValue_duration);

        // 将 jboolean 转换为 Boolean 对象
        jclass booleanClass = env->FindClass("java/lang/Boolean");
        jmethodID booleanConstructor = env->GetMethodID(booleanClass, "<init>", "(Z)V");
        jobject jboolObject_valid = env->NewObject(booleanClass, booleanConstructor, jboolValue_valid);
        jobject jboolObject_skew = env->NewObject(booleanClass, booleanConstructor, jboolValue_skew);

        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("valid"), jboolObject_valid);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("skew"), jboolObject_skew);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("gaze_x"), jfloatObject_x);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("gaze_y"), jfloatObject_y);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("dist"), jfloatObject_dist);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("duration"), jfloatObject_duration);

        env->ReleaseByteArrayElements(inputImage_, inputImage, 0);
    }
    return hashMap;
}

// ****************************** tracker-end ***********************************

// ****************************** calibration-start *********************************
/**
 * 开始眼动校准(视标校准)
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeStartCalibration(JNIEnv *env, jclass clazz, jlong thiz) {
    bool flag = false;
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        flag = tracker_service->start_calibration();
        finish_calib = false;
    }
    return flag;
}

/**
 * 停止眼动校准(视标校准)
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeStopCalibration(JNIEnv *env, jclass clazz, jlong thiz) {
    bool flag = false;
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        flag = tracker_service->stop_calibration();
    }
    return flag;
}

/**
 * 眼动校准：眼动校准处理函数
 */
extern "C"
JNIEXPORT jobject JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeCalibrating(JNIEnv *env, jclass clazz, jlong thiz,
                                                   jbyteArray inputImage_, jint width, jint height) {
    jclass hashMapClass = env->FindClass("java/util/HashMap");
    jmethodID hashMapConstructor = env->GetMethodID(hashMapClass, "<init>", "()V");
    jmethodID hashMapPut = env->GetMethodID(hashMapClass, "put", "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;");

    jobject hashMap = env->NewObject(hashMapClass, hashMapConstructor);
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        if (!finish_calib) {
            // 将图片数据转化为jbyte
            jbyte *inputImage = env->GetByteArrayElements(inputImage_, nullptr);
            // 根据I420宽高，设置Mat（OpenCV支持的图片格式）的宽高，并赋值 src
            Mat grayimg(height, width, CV_8UC1, (unsigned char*)inputImage);

            // 眼动校准
            calibrating_result_str result = tracker_service->process_calibration(grayimg);
            Mat rgba_visual; // RGBA image
            cvtColor(tracker_service->visual_calibration_draw_func(), rgba_visual, COLOR_BGR2RGBA);
//            tracker_service->draw(rgba_visual);

            auto jintValue_left_num = static_cast<jint>(result.left_consist_num);
            auto jintValue_right_num = static_cast<jint>(result.right_consist_num);
            auto jboolValue_finih = static_cast<jboolean>(result.finish_flag);

            // 将 jboolean 转换为 jint 对象
            jclass intClass = env->FindClass("java/lang/Integer");
            jmethodID intConstructor = env->GetMethodID(intClass, "<init>", "(I)V");
            jobject jintObject_left_num = env->NewObject(intClass, intConstructor, jintValue_left_num);
            jobject jintObject_right_num = env->NewObject(intClass, intConstructor, jintValue_right_num);
            // 将 jboolean 转换为 Boolean 对象
            jclass booleanClass = env->FindClass("java/lang/Boolean");
            jmethodID booleanConstructor = env->GetMethodID(booleanClass, "<init>", "(Z)V");
            jobject jboolObject_finish = env->NewObject(booleanClass, booleanConstructor, jboolValue_finih);

            env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("finish_calib"), jboolObject_finish);
            env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("left_consist_num"), jintObject_left_num);
            env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("right_consist_num"), jintObject_right_num);

            env->ReleaseByteArrayElements(inputImage_, inputImage, 0);
        }
    }
    return hashMap;
}

// ****************************** calibration-end ***********************************

// ****************************** poseAlign-start *************************************
 /**
  * 开始姿势校准
  */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeStartPostureCalibration(JNIEnv *env, jclass clazz, jlong thiz) {
    bool flag = false;
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        flag = tracker_service->start_aligning_head_pose(true);
    }
    return flag;
}

 /**
  * 开始姿势矫正
  */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeStartPostureCorrection(JNIEnv *env, jclass clazz, jlong thiz) {
    bool flag = false;
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        flag = tracker_service->start_aligning_head_pose(false);
    }
    return flag;
}

/**
 * 停止姿势校准
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeStopPostureCalibration(JNIEnv *env, jclass clazz, jlong thiz) {
    bool flag = false;
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        flag = tracker_service->stop_aligning_head_pose();
    }
    return flag;
}

 //姿势矫正处理函数
extern "C"
JNIEXPORT jobject JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativePostureCorrection(JNIEnv *env, jclass clazz, jlong thiz,
                                                 jbyteArray inputImage_, jint width, jint height) {
    jclass hashMapClass = env->FindClass("java/util/HashMap");
    jmethodID hashMapConstructor = env->GetMethodID(hashMapClass, "<init>", "()V");
    jmethodID hashMapPut = env->GetMethodID(hashMapClass, "put", "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;");

    jobject hashMap = env->NewObject(hashMapClass, hashMapConstructor);
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        // 将图片数据转化为jbyte
        jbyte *inputImage = env->GetByteArrayElements(inputImage_, nullptr);
        // 根据I420宽高，设置Mat（OpenCV支持的图片格式）的宽高，并赋值 src
        Mat grayimg(height, width, CV_8UC1, (unsigned char*)inputImage);

        pose_align_result result = tracker_service->process_poseAlign(grayimg); // 调用C++函数获取结果

        Mat bgr_visual, rgba_visual; // visual image
        bgr_visual = tracker_service->visual_pose_align_func(grayimg);

        cvtColor(bgr_visual, rgba_visual, COLOR_BGR2RGBA);
//        tracker_service->draw(rgba_visual);

        jboolean jboolResult_state = result.pose_aligned;
        jboolean jboolResult_aligned = result.aligning;
        jint jintResult_countdown = result.countdown;
        jfloat jfloatResult_dist = result.dist;
        jfloat jfloatResult_right_x = result.right_pupil.x;
        jfloat jfloatResult_right_y = result.right_pupil.y;
        jfloat jfloatResult_left_x = result.left_pupil.x;
        jfloat jfloatResult_left_y = result.left_pupil.y;

        auto jboolValue_state = static_cast<jboolean>(jboolResult_state);
        auto jboolValue_aligned = static_cast<jboolean>(jboolResult_aligned);
        auto jfloatValue_dist = static_cast<jfloat>(jfloatResult_dist);
        auto jintValue_countdown = static_cast<jint>(jintResult_countdown);

        auto jfloatValue_right_x = static_cast<jfloat>(jfloatResult_right_x);
        auto jfloatValue_right_y = static_cast<jfloat>(jfloatResult_right_y);
        auto jfloatValue_left_x = static_cast<jfloat>(jfloatResult_left_x);
        auto jfloatValue_left_y = static_cast<jfloat>(jfloatResult_left_y);


        // 将 jboolean 转换为 Boolean 对象
        jclass booleanClass = env->FindClass("java/lang/Boolean");
        jmethodID booleanConstructor = env->GetMethodID(booleanClass, "<init>", "(Z)V");
        jobject jboolObject_state = env->NewObject(booleanClass, booleanConstructor, jboolValue_state);
        jobject jboolObject_aligned = env->NewObject(booleanClass, booleanConstructor, jboolValue_aligned);

        // 将 jfloat 转换为 Float 对象
        jclass floatClass = env->FindClass("java/lang/Float");
        jmethodID floatConstructor = env->GetMethodID(floatClass, "<init>", "(F)V");
        jobject jfloatObject_dist = env->NewObject(floatClass, floatConstructor, jfloatValue_dist);
        jobject jfloatObject_right_x = env->NewObject(floatClass, floatConstructor, jfloatValue_right_x);
        jobject jfloatObject_right_y = env->NewObject(floatClass, floatConstructor, jfloatValue_right_y);
        jobject jfloatObject_left_x = env->NewObject(floatClass, floatConstructor, jfloatValue_left_x);
        jobject jfloatObject_left_y = env->NewObject(floatClass, floatConstructor, jfloatValue_left_y);

        jclass intClass = env->FindClass("java/lang/Integer");
        jmethodID intConstructor = env->GetMethodID(intClass, "<init>", "(I)V");
        jobject jintObject_countdown = env->NewObject(intClass, intConstructor, jintValue_countdown);


        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("state"), jboolObject_state);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("dist"), jfloatObject_dist);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("aligned"), jboolObject_aligned);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("countdown"), jintObject_countdown);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("right_x"), jfloatObject_right_x);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("right_y"), jfloatObject_right_y);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("left_x"), jfloatObject_left_x);
        env->CallObjectMethod(hashMap, hashMapPut, env->NewStringUTF("left_y"), jfloatObject_left_y);

        env->ReleaseByteArrayElements(inputImage_, inputImage, 0);
    }

    return hashMap;
}

// ****************************** poseAlign-end *************************************

// *************** upload data **********************
// 构造函数 -> 云服务上传数据
extern "C"
JNIEXPORT jlong JNICALL
Java_com_airdoc_mpd_gaze_upload_UploadCloud_nativeCreateObject(JNIEnv *env, jclass clazz) {
    auto *upload = new upload2Cloud();

    // 返回upload2Cloud地址给Java层
    return (jlong) upload;
}

/**
 * 云服务器上传：开始上传数据
 */
extern "C"
JNIEXPORT void JNICALL
Java_com_airdoc_mpd_gaze_upload_UploadCloud_nativeStartUploadCloud(JNIEnv *env, jclass clazz, jlong thiz) {
    if (thiz != 0) {
        auto *upload = reinterpret_cast<upload2Cloud *>(thiz);
        upload->startUpload2Cloud();
    }
}



// *************** upload end **********************


// *************** Application *********************
// 创建实例
extern "C"
JNIEXPORT jlong JNICALL
Java_com_airdoc_mpd_gaze_application_GazeApplied_nativeCreateGazeApplied(JNIEnv *env, jclass clazz)
{
    auto *application = new GazeApplication();

    // 返回GazeApplication实例地址给Java层
    return (jlong) application;
}

/**
 * 设置眼动追踪回调
 */
extern "C"
JNIEXPORT void JNICALL
Java_com_airdoc_mpd_gaze_application_GazeApplied_nativeSetGazeAppliedCallback(JNIEnv* env, jclass obj, jlong thiz, jobject callback_calib) {
    if (thiz != 0) {
        auto *application = reinterpret_cast<GazeApplication *>(thiz);
        application->setGazeAppliedCallback(env, callback_calib);
    }
}

/**
 * 眼动应用：启动应用过程
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_application_GazeApplied_nativeStartApplication(JNIEnv *env, jclass clazz, jlong thiz, jint mode) {
    bool flag = false;
    if (thiz != 0) {
        auto *application = reinterpret_cast<GazeApplication *>(thiz);
        flag = application->start_application(mode);
    }
    return flag;
}

/**
 * 眼动应用：停止应用过程
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_application_GazeApplied_nativeStopApplication(JNIEnv *env, jclass clazz, jlong thiz) {
    bool flag = false;
    if (thiz != 0) {
        auto *application = reinterpret_cast<GazeApplication *>(thiz);
        flag = application->stop_application();
    }
    return flag;
}

/**
 * 眼动应用：设置屏幕虚化的参数
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_application_GazeApplied_nativeSetBlurParams(JNIEnv *env, jclass clazz, jlong thiz,
                                                                            jfloat radius, jfloat sigma, jint blur_mode, jint blur_ch) {
    bool flag = false;
    if (thiz != 0) {
        auto *application = reinterpret_cast<GazeApplication *>(thiz);
        flag = application->set_blur_params_func(radius, sigma, blur_mode, blur_ch);
    }
    return flag;
}

/**
 * 眼动应用：设置注视的目标点
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_application_GazeApplied_nativeSetStarePoint(JNIEnv *env, jclass clazz, jlong thiz, jfloat x, jfloat y) {
    bool flag = false;
    if (thiz != 0) {
        auto *application = reinterpret_cast<GazeApplication *>(thiz);
        flag = application->set_stare_point_func(x, y);
    }
    return flag;
}

/**
 * 眼动应用：设置扫视的目标点
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_application_GazeApplied_nativeSetGlancePoint(JNIEnv *env, jclass clazz, jlong thiz, jfloat x, jfloat y) {
    bool flag = false;
    if (thiz != 0) {
        auto *application = reinterpret_cast<GazeApplication *>(thiz);
        flag = application->set_glance_point_func(x, y);
    }
    return flag;
}

/**
 * 眼动应用：将实时的眼动数据传入应用层
 */
extern "C"
JNIEXPORT jboolean JNICALL
Java_com_airdoc_mpd_gaze_application_GazeApplied_nativeCollectGaze(JNIEnv *env, jclass clazz, jlong thiz,
                                                                          jboolean valid, jfloat x, jfloat y, jfloat dist, jfloat duration) {
    bool flag = false;
    if (thiz != 0) {
        auto *application = reinterpret_cast<GazeApplication *>(thiz);
        flag = application->collect_gaze(valid, x, y, dist, duration);
    }
    return flag;
}

/**
 * 眼动应用：获取眼动轨迹数据结果
 */
extern "C"
JNIEXPORT jstring JNICALL
Java_com_airdoc_mpd_gaze_application_GazeApplied_nativeGetGazeTrajectory(JNIEnv *env, jclass clazz, jlong thiz, jint mode) {
    string jsondata = "";
    if (thiz != 0) {
        auto *application = reinterpret_cast<GazeApplication *>(thiz);
        jsondata = application->get_record_data_func(mode);
    }
    // 返回 JSON 字符串
    return env->NewStringUTF(jsondata.c_str());
}

// ****************** end **************************

/**
 * ----- 释放类对象 -----
 */
extern "C"
JNIEXPORT void JNICALL
Java_com_airdoc_mpd_gaze_track_GazeTrack_nativeDestroyObject(JNIEnv *env, jclass clazz, jlong thiz) {
    if (thiz != 0) {
        auto *tracker_service = reinterpret_cast<GazeService *>(thiz);
        delete tracker_service;
    }
}

extern "C"
JNIEXPORT void JNICALL
Java_com_airdoc_mpd_gaze_upload_UploadCloud_nativeDestroyObject(JNIEnv *env, jclass clazz, jlong thiz) {
    if (thiz != 0) {
        auto *upload = reinterpret_cast<upload2Cloud *>(thiz);
        delete upload;
    }
}

extern "C"
JNIEXPORT void JNICALL
Java_com_airdoc_mpd_gaze_application_GazeApplied_nativeDestroyGazeApplied(JNIEnv *env, jclass clazz, jlong thiz) {
    if (thiz != 0) {
        auto *application = reinterpret_cast<GazeApplication *>(thiz);
        delete application;
    }
}
