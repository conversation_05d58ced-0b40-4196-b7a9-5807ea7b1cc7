package com.airdoc.mpd.ppg.websocket

import android.content.Context
import com.airdoc.component.common.log.Logger
import com.airdoc.mpd.utils.NetworkUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.InetSocketAddress


class PpgWebSocketManager private constructor() {
    
    companion object {
        private val TAG = PpgWebSocketManager::class.java.simpleName
        private const val DEFAULT_PORT = 9201 // 使用不同于GazeWebSocket的端口
        
        @Volatile
        private var INSTANCE: PpgWebSocketManager? = null
        
        fun getInstance(): PpgWebSocketManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PpgWebSocketManager().also { INSTANCE = it }
            }
        }
    }
    
    private var ppgWebSocketService: PpgWebSocketService? = null
    private var isServiceRunning = false
    private var currentWebSocketAddress: String? = null
    
    /**
     * 启动PPG WebSocket服务
     * @param context 上下文
     * @param port 端口号，默认为9201
     * @return 是否启动成功
     */
    suspend fun startWebSocketService(context: Context, port: Int = DEFAULT_PORT): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 如果服务已经在运行，先停止
                if (isServiceRunning) {
                    Logger.d(TAG, msg = "PPG WebSocket服务已在运行，先停止旧服务")
                    stopWebSocketService()
                }
                
                // 动态获取设备IP地址
                val deviceIP = NetworkUtils.getDeviceIPAddress()
                
                Logger.d(TAG, msg = "准备启动PPG WebSocket服务器")
                Logger.d(TAG, msg = "  设备IP: $deviceIP")
                Logger.d(TAG, msg = "  端口: $port")
                
                val inetSocketAddress = if (deviceIP.isNotEmpty()) {
                    InetSocketAddress(deviceIP as String, port)
                } else {
                    // 如果无法获取IP，绑定到所有接口
                    Logger.w(TAG, msg = "无法获取设备IP，绑定到所有接口")
                    InetSocketAddress(port)
                }
                
                ppgWebSocketService = PpgWebSocketService(inetSocketAddress).apply {
                    isReuseAddr = true
                }
                ppgWebSocketService?.start()
                
                val actualIP = if (deviceIP.isNotEmpty()) deviceIP else "0.0.0.0"
                currentWebSocketAddress = "ws://$actualIP:$port"
                isServiceRunning = true
                
                Logger.i(TAG, msg = "✓ PPG WebSocket服务器启动成功")
                Logger.i(TAG, msg = "  监听IP: $actualIP")
                Logger.i(TAG, msg = "  监听端口: $port")
                Logger.i(TAG, msg = "  Web连接地址: $currentWebSocketAddress")
                
                true
            } catch (e: Exception) {
                Logger.e(TAG, msg = "PPG WebSocket服务器启动失败: ${e.message}")
                Logger.e(TAG, msg = "  错误详情: ${e.stackTraceToString()}")
                isServiceRunning = false
                currentWebSocketAddress = null
                false
            }
        }
    }
    
    /**
     * 停止PPG WebSocket服务
     */
    fun stopWebSocketService() {
        try {
            ppgWebSocketService?.let {
                Logger.d(TAG, msg = "正在停止PPG WebSocket服务器")
                it.stop()
                Logger.i(TAG, msg = "✓ PPG WebSocket服务器已停止")
            }
            ppgWebSocketService = null
            isServiceRunning = false
            currentWebSocketAddress = null
        } catch (e: Exception) {
            Logger.e(TAG, msg = "停止PPG WebSocket服务器失败: ${e.message}")
        }
    }
    
    /**
     * 检查服务是否正在运行
     */
    fun isServiceRunning(): Boolean = isServiceRunning
    
    /**
     * 获取当前WebSocket连接地址
     */
    fun getCurrentWebSocketAddress(): String? = currentWebSocketAddress
    
    /**
     * 广播PPG数据到WebSocket客户端
     * @param data PPG数据，JSON格式字符串
     */
    fun broadcastPpgData(data: String) {
        try {
            ppgWebSocketService?.let { service ->
                service.broadcast(data)
                Logger.d(TAG, msg = "WebSocket广播PPG数据: $data")
            } ?: run {
                Logger.w(TAG, msg = "PPG WebSocket服务未启动，无法广播数据")
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "WebSocket广播PPG数据失败: ${e.message}")
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        stopWebSocketService()
        INSTANCE = null
    }
}
