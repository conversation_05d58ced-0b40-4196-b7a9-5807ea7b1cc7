#include "utils.h"
#include <cfloat>

int get_type_size(rknn_tensor_type type){
    switch (type)
    {
    case RKNN_TENSOR_FLOAT32: return 4;
    case RKNN_TENSOR_FLOAT16: return 2;
    case RKNN_TENSOR_UINT8: return 1;
    case RKNN_TENSOR_INT8: return 1;
    default: printf("ERROR: not support tensor type %s", get_type_string(type)); return -1;
    }
}

void check_ret(int ret, const char* msg){
    if (ret < 0){
        printf("ERROR: %s failed! ret=%d\n", msg, ret);
        exit(-1);
    }
    else
    {
        printf("INFO: %s succeed! ret=%d\n", msg, ret);
    }
}

// Sigmoid function with scaling and shifting.
double distance_sigmoid(double dist_ratio)
{
    /* function: alpha * (1.0 / (1.0 + exp(-k * (x - x0))) - beta) + gamma
     * k = 2.6, alpha = 1.0346, x0 = 0.994, beta = -0.83, gamma = -0.88
     */
    double y = 1.0346 * (1.0 / (1.0 + exp(-2.6 * (dist_ratio - 0.994))) + 0.83) - 0.88;
    return std::min(std::max(y, 0.0), 1.0);
}

// --- 估算人眼离屏幕的距离 ---
double map_iris_len_pixel_to_dist_func(double iris_width)
{
    // IMX577的参数：f: 焦距 7mm,  p：pixel size 1.55um
    // 眼动仪：f: 焦距 5.54mm，p：pixel size 1.12um
    // L: 虹膜直径 1.26cm
    //  相似三角形原理 D = fxL / l  ->  D: 距离，f: 焦距 5.54mm，L: 物体长度 1.26cm，l: 图像上的物体长度 n * p
    double eye_dist = -1;
    if(iris_width > 0)
    {
        double len_iris_diam = iris_width * 1.55 * 0.001 * 0.1;  // 单位cm； 1.55/1.12是像元尺寸
        eye_dist = 0.75 * 1.26 / len_iris_diam;   // 单位是cm；1.2是虹膜的实际大小
    }
    return eye_dist;
}


void find_best_point_by_prob_mask(cv::Mat &src, cv::Point& cent_point, float& maxval, int width, int height, float threshold)
{
    float best_value = 0.0;
    int cent_x = -1;
    int cent_y = -1;
    for (int i = 0; i < height; i++)
    {
        auto* pdata= src.ptr<float>(i);
        for (int j = 0; j < width; j++)
        {
            float confidence = pdata[j];
            if (confidence < threshold)
            {
                continue;
            }
            else
            {
                if (confidence > best_value)
                {
                    best_value = confidence;
                    cent_x = j;
                    cent_y = i;
                }
            }
        }
    }

    if (best_value > threshold)
    {
        cent_point.x = cent_x;
        cent_point.y = cent_y;
        maxval = best_value;
    }
}

void get_mask_value_range(const cv::Mat &mask, float& min_val, float& max_val)
{
    min_val = FLT_MAX;
    max_val = -FLT_MAX;

    for (int i = 0; i < mask.rows; i++)
    {
        const float* pdata = mask.ptr<float>(i);
        for (int j = 0; j < mask.cols; j++)
        {
            float value = pdata[j];
            if (value < min_val) min_val = value;
            if (value > max_val) max_val = value;
        }
    }
}

void find_boundingRect_from_mask(cv::Mat &src, cv::Rect& bbox)
{
    int width = src.cols;
    int height = src.rows;

    int minx = width;
    int miny = height;
    int maxx = -1;
    int maxy = -1;
    for (int i = 0; i < height; i++)
    {
        auto* pdata= src.ptr<uchar>(i);
        for (int j = 0; j < width; j++)
        {
            uchar value = pdata[j];
            if (value == 255)
            {
                if (minx > j)
                {
                    minx = j;
                }
                if (maxx < j)
                {
                    maxx = j;
                }
                if (miny > i)
                {
                    miny = i;
                }
                if (maxy < i)
                {
                    maxy = i;
                }
            }
            else
            {
                continue;
            }
        }
    }

    if (maxy - miny > 10 && maxx - minx > 10)
    {
        bbox.x = minx;
        bbox.y = miny;
        bbox.width = maxx - minx;
        bbox.height = maxy - miny;
    }
}

void manual_threahold_float(const cv::Mat &src, cv::Mat& dst, float threshold, int set_value)
{
    // 手动实现阈值操作并使用指针遍历
    int rows = src.rows;
    int cols = src.cols;
    uchar* pDst = dst.data; // 获取dst的指针

    for(int i = 0; i < rows; ++i)
    {
        const auto* pSrc = src.ptr<float>(i); // 获取src的指针
        for(int j = 0; j < cols; ++j)
        {
            float pixelValue = pSrc[j]; // 通过指针访问src的像素值
            // 手动进行二值化操作
            pDst[i * cols + j] = (pixelValue > threshold) ? set_value : 0; // 如果像素值大于阈值，设置为255（白色），否则为0（黑色）
        }
    }
}

void manual_threahold_uchar(const cv::Mat &src, cv::Mat& dst, int threshold, int set_value)
{
    // 手动实现阈值操作并使用指针遍历
    int rows = src.rows;
    int cols = src.cols;
    uchar* pDst = dst.data; // 获取dst的指针

    for(int i = 0; i < rows; ++i)
    {
        const auto* pSrc = src.ptr<uchar>(i); // 获取src的指针
        for(int j = 0; j < cols; ++j)
        {
            uchar pixelValue = pSrc[j]; // 通过指针访问src的像素值
            // 手动进行二值化操作
            pDst[i * cols + j] = (pixelValue > threshold) ? set_value : 0; // 如果像素值大于阈值，设置为255（白色），否则为0（黑色）
        }
    }
}

void create_eye_tracking_rect_func(cv::Rect &iris_rect, cv::Rect &search_rect, int imgw, int imgh)
{
    int cx = iris_rect.x + iris_rect.width / 2;
    int cy = iris_rect.y + iris_rect.height / 2;
    int diameter = std::max(iris_rect.width, iris_rect.height);
    int width = (int)((float)diameter * 1.2);
    int height = (int)((float)width * 0.8);

    int new_x0 = std::max(0, cx - width);
    int new_x1 = std::min(imgw, cx + width);
    int new_y0 = std::max(0, cy - height);
    int new_y1 = std::min(imgh, cy + height);

    search_rect = Rect(new_x0, new_y0, new_x1 - new_x0, new_y1 - new_y0);
}

void calculate_2points_state(const std::vector<cv::Point2f> & lights_point_list, vector<bool>& state_list)
{
    state_list.clear();
    for (int i = 0; i < 2; i++)
    {
        if (lights_point_list[i].x > 0 && lights_point_list[i].y > 0)
        {
            state_list.push_back(true);
        }
        else
        {
            state_list.push_back(false);
        }
    }
}

void refine_lights_pixel(const cv::Mat &src, const cv::Point& ori_light_point, cv:: Point2f& light_point)
{
    int lx = std::max(0, ori_light_point.x - refine_radius);
    int ly = std::max(0, ori_light_point.y - refine_radius);
    int rx = std::min(src.cols, ori_light_point.x + refine_radius);
    int ry = std::min(src.rows, ori_light_point.y + refine_radius);
    int rect_w = rx - lx;
    int rect_h = ry - ly;
    cv::Rect roi_rect = cv::Rect(lx, ly, rect_w, rect_h);

    Mat patch = src(roi_rect).clone();
    int max_value = findMaxInPatch(patch);
    int light_threshold = (int)((float)max_value * refine_ratio);

    Mat seg_mask = cv::Mat::zeros(patch.rows, patch.cols, CV_8UC1);
    // cv::threshold(patch, seg_mask, light_threshold, 1, cv::THRESH_BINARY);
    manual_threahold_uchar(patch, seg_mask, light_threshold, 1);
    cv::Point2f cent_point = findForegroundCenter(seg_mask);
    light_point.x = cent_point.x + (float)lx;
    light_point.y = cent_point.y + (float)ly;
}

// 实现函数：在给定的图像patch中找到最大像素值

int findMaxInPatch(const cv::Mat& img) {
    uchar* data = img.data;
    size_t step = img.step;
    int rows = img.rows;
    int cols = img.cols;
    uchar maxVal = 0;
    for (int row = 0; row < rows; ++row) {
        for (int col = 0; col < cols; ++col) {
            uchar val = data[row * step + col];
            if (val > maxVal) {
                maxVal = val;
            }
        }
    }

    return (int)maxVal;
}

// 实现函数：计算二值掩码中前景区域的中心点坐标
bool findForegroundCenter(const cv::Mat& mask, cv::Point2f& center) {
    center = cv::Point2f(0.0F, 0.0F); // 初始化中心点
    int foregroundCount = 0; // 前景像素计数

    int min_x = 4048;
    int max_x = 0;
    int min_y = 3040;
    int max_y = 0;
    bool not_blink = false;

    // 遍历掩码的每一个像素
    for (int y = 0; y < mask.rows; ++y) {
        const auto* rowPtr = mask.ptr<uchar>(y);
        for (int x = 0; x < mask.cols; ++x) {
            if (rowPtr[x] == 1) { // 如果是前景像素
                center.x += (float)x; // 累加x坐标
                center.y += (float)y; // 累加y坐标
                ++foregroundCount; // 前景像素计数增加

                if(x > max_x){max_x = x;}
                if(x < min_x){min_x = x;}
                if(y > max_y){max_y = y;}
                if(y < min_y){min_y = y;}
            }
        }
    }

    // 如果有前景像素，计算平均值作为中心点；否则返回原点
    if (foregroundCount > 0) {
        center.x /= (float)foregroundCount;
        center.y /= (float)foregroundCount;
    }
    // 判断是否眨眼
    if (max_x > min_x && max_y > min_y)
    {
        float pupil_ratio = (float)(max_y - min_y) / (float)(max_x - min_x);
        if (pupil_ratio > blink_pupil_ratio_thres)
        {
            not_blink = true;
        }
    }

    return not_blink;
}

// 实现函数：计算二值掩码中前景区域的中心点坐标
cv::Point2f findForegroundCenter(const cv::Mat& mask) {
    cv::Point2f center(0, 0); // 初始化中心点
    int foregroundCount = 0; // 前景像素计数

    // 遍历掩码的每一个像素
    for (int y = 0; y < mask.rows; ++y) {
        const auto* rowPtr = mask.ptr<uchar>(y);
        for (int x = 0; x < mask.cols; ++x) {
            if (rowPtr[x] == 1) { // 如果是前景像素
                center.x += (float)x; // 累加x坐标
                center.y += (float)y; // 累加y坐标
                ++foregroundCount; // 前景像素计数增加
            }
        }
    }

    // 如果有前景像素，计算平均值作为中心点；否则返回原点
    if (foregroundCount > 0) {
        center.x /= (float)foregroundCount;
        center.y /= (float)foregroundCount;
    }

    return center;
}

// 实现函数： 根据宽和高的比例修改bbox，再画图
void rectangle_by_ratio(cv::Mat &img, cv::Rect bbox, const cv::Scalar& color, int thickness, float ratio_w, float ratio_h)
{
    if(bbox.x >=0 && bbox.y >= 0 && bbox.width > 1 && bbox.height > 1)
    {
        cv::Rect new_bbox;
        new_bbox.x = (int)((float)bbox.x * ratio_w);
        new_bbox.y = (int)((float)bbox.y * ratio_h);
        new_bbox.width = (int)((float)bbox.width * ratio_w);
        new_bbox.height = (int)((float)bbox.height * ratio_h);

        cv::rectangle(img, new_bbox, color, thickness);
    }

}

void circle_by_ratio(cv::Mat &img, cv::Point cp, const cv::Scalar& color, int thickness, int radius, float ratio_w, float ratio_h)
{
    if(cp.x > 0 && cp.y > 0)
    {
        cv::Point new_point;
        new_point.x = (int)((float)cp.x * ratio_w);
        new_point.y = (int)((float)cp.y * ratio_h);
        circle(img, new_point, radius, color, thickness);
    }
}

// 判断文件是否存在
bool fileExists(const std::string& path) {
    return fs::exists(path);
}

// 判断数组维数是否符合要求
bool checkVectorDimensions(const std::vector<std::vector<double>>& input_param) {
    // 首先检查总行数是否为6
    if (input_param.size() != 6) {
        return false;
    }

    // 预定义一个数组来存储预期的列数
    int expectedColumns[] = {4, 4, 4, 4, 4, 4};

    // 遍历每一行进行检查
    for (size_t i = 0; i < input_param.size(); ++i) {
        // 检查当前行的大小（列数）是否与预期相符
        if (input_param[i].size() != expectedColumns[i]) {
            return false;
        }
    }
    // 如果所有检查都通过，则返回true
    return true;
}

/*
    --- 读和写Json文件 ---
 */

// 将vector<vector<float>>转换为Json::Value
Json::Value vectorToJson(const std::vector<std::vector<double>>& vecVecFloat) {
    Json::Value jsonArray(Json::arrayValue);
    for (const auto& innerVec : vecVecFloat) {
        Json::Value innerJsonArray(Json::arrayValue);
        for (double val : innerVec) {
            innerJsonArray.append(val);
        }
        jsonArray.append(innerJsonArray);
    }
    return jsonArray;
}

// 写入校准中间结果到JSON文件
void writeDebugParamsToFile(const std::string& filename,
                            const calibration_params_data_str& calib_datas) {
    LOGI("------- writeDebugParamsToFile: save Parameters to %s ------", filename.c_str());
    Json::Value root;
    root["leftData"] = vectorToJson(calib_datas.left_data);
    root["rightData"] = vectorToJson(calib_datas.right_data);
    root["leftParams"] = vectorToJson(calib_datas.left_params);
    root["rightParams"] = vectorToJson(calib_datas.right_params);
    Json::StreamWriterBuilder writerBuilder;
    writerBuilder["indentation"] = "  ";
    std::unique_ptr<Json::StreamWriter> writer(writerBuilder.newStreamWriter());
    std::ofstream ofs(filename);
    if (!ofs.is_open()) {
        std::cerr << "Failed to open file for writing." << std::endl;
        return;
    }
    writer->write(root, &ofs);
    ofs.close();
}

// 写入JSON文件
void writeConfigToFile(const std::string& filename, mapping_params_str& map_params) {
    LOGI("------- writeConfigToFile: leftData： ------");
    printf_2d_vector(map_params.left_params);

    LOGI("------- writeConfigToFile: rightData： ------");
    printf_2d_vector(map_params.right_params);

    if(map_params.valid)
    {
        Json::Value root;
        root["left"] = vectorToJson(map_params.left_params);
        root["right"] = vectorToJson(map_params.right_params);
        Json::StreamWriterBuilder writerBuilder;
        writerBuilder["indentation"] = "  ";
        std::unique_ptr<Json::StreamWriter> writer(writerBuilder.newStreamWriter());
        std::ofstream ofs(filename);
        if (!ofs.is_open()) {
            std::cerr << "Failed to open file for writing." << std::endl;
            return;
        }
        writer->write(root, &ofs);
        ofs.close();
    }
}

// 写入JSON文件
void writePoseToFile(const std::string& filename,
                     const cv::Point& left_p, const cv::Point& right_p, double dist) {

    std::vector<std::vector<double>> eye_point_vec;
    eye_point_vec.push_back({(double)left_p.x, (double)left_p.y});
    eye_point_vec.push_back({(double)right_p.x, (double)right_p.y});
    eye_point_vec.push_back({dist});

    LOGI("------- writePoseToFile: filename： %s------", filename.c_str());
    LOGI("------- writePoseToFile: pose data： ------");
    printf_2d_vector(eye_point_vec);

    Json::Value root;
    root["pose"] = vectorToJson(eye_point_vec);
    Json::StreamWriterBuilder writerBuilder;
    writerBuilder["indentation"] = "  ";
    std::unique_ptr<Json::StreamWriter> writer(writerBuilder.newStreamWriter());
    std::ofstream ofs(filename);
    if (!ofs.is_open()) {
        std::cerr << "Failed to open file for writing." << std::endl;
        return;
    }
    writer->write(root, &ofs);
    ofs.close();
}

// 从Json::Value中解析出vector<vector<float>>
std::vector<std::vector<double>> jsonToVector(const Json::Value& jsonArray) {
    std::vector<std::vector<double>> result;
    for (const auto& elem : jsonArray) {
        std::vector<double> innerVec;
        for (const auto & i : elem) {
            innerVec.push_back(i.asFloat());
        }
        result.push_back(innerVec);
    }
    return result;
}

// 从文件中读取JSON配置
bool readConfigFromFile(const std::string& filename, mapping_params_str& map_params) {
    LOGI("------- readConfigFromFile： ------");
    std::ifstream ifs(filename);
    if (!ifs.is_open()) {
        std::cerr << "Failed to open file for reading." << std::endl;
        return false;
    }

    std::stringstream buffer;
    buffer << ifs.rdbuf(); // 读取整个文件到缓冲区
    std::string strContent = buffer.str(); // 获取文件内容字符串

    Json::CharReaderBuilder builder;
    std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
    Json::Value root;
    std::string errs;

    bool parsingSuccessful = reader->parse(strContent.c_str(), strContent.c_str() + strContent.size(), &root, &errs);
    if (!parsingSuccessful) {
        std::cerr << "Failed to parse JSON: " << errs << std::endl;
        return false;
    }
    map_params.valid = true;
    map_params.left_params = jsonToVector(root["left"]);
    map_params.right_params = jsonToVector(root["right"]);

    LOGI("------- readConfigFromFile: leftData： ------");
    printf_2d_vector(map_params.left_params);

    LOGI("------- readConfigFromFile: rightData： ------");
    printf_2d_vector(map_params.right_params);

    ifs.close();
    return true;
}

// 从文件中读取JSON配置
bool readPoseFromFile(const std::string& filename,
                        cv::Point& left_p, cv::Point& right_p, double& dist) {
    std::ifstream ifs(filename);
    if (!ifs.is_open()) {
        std::cerr << "Failed to open file for reading." << std::endl;
        return false;
    }

    std::stringstream buffer;
    buffer << ifs.rdbuf(); // 读取整个文件到缓冲区
    std::string strContent = buffer.str(); // 获取文件内容字符串

    Json::CharReaderBuilder builder;
    std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
    Json::Value root;
    std::string errs;

    bool parsingSuccessful = reader->parse(strContent.c_str(), strContent.c_str() + strContent.size(), &root, &errs);
    if (!parsingSuccessful) {
        std::cerr << "Failed to parse JSON: " << errs << std::endl;
        return false;
    }

    std::vector<std::vector<double>> eye_point_vec = jsonToVector(root["pose"]);

    LOGD("------- readPoseFromFile: pose data： ------");
    LOGD("------- readPoseFromFile: eye_point_vec.size() = %d： ------", (int)eye_point_vec.size());
    LOGD("------- readPoseFromFile: eye_point_vec[0].size() = %d： ------", (int)eye_point_vec[0].size());
    printf_2d_vector(eye_point_vec);

    left_p = cv::Point((int)eye_point_vec[0][0], (int)eye_point_vec[0][1]);
    right_p = cv::Point((int)eye_point_vec[1][0], (int)eye_point_vec[1][1]);
    dist = eye_point_vec[2][0];

    ifs.close();
    return true;
}

std::vector<std::vector<double>> convertToDouble(const std::vector<std::vector<float>>& input) {
    std::vector<std::vector<double>> output;
    output.reserve(input.size()); // 预留足够的空间以优化性能
    for(const auto& row : input) {
        std::vector<double> tempRow;
        tempRow.reserve(row.size()); // 同样预留空间
        for(const float& value : row) {
            tempRow.push_back(static_cast<double>(value)); // 类型转换
        }
        output.push_back(std::move(tempRow)); // 移动语义，避免拷贝
    }

    return output;
}

void printf_2d_vector(const vector<vector<float>>& data)
{
    for (const auto& row : data) {
        std::stringstream ss;
        ss << std::fixed << std::setprecision(printf_num); // 设置浮点数输出格式：保留3位小数
        for (const auto& element : row) {
            ss << element;
            ss << ", ";
        }
        std::string row_str = ss.str();
        LOGI("%s", row_str.c_str());
    }
}

void printf_2d_vector(const vector<vector<double>>& data)
{
    for (const auto& row : data) {
        std::stringstream ss;
        ss << std::fixed << std::setprecision(printf_num); // 设置浮点数输出格式：保留3位小数
        for (const auto& element : row) {
            ss << element;
            ss << ", ";
        }
        std::string row_str = ss.str();
        LOGI("%s", row_str.c_str());
    }
}

void printf_1d_vector(const vector<float>& data)
{
    std::stringstream ss;
    ss << std::fixed << std::setprecision(printf_num); // 设置浮点数输出格式：保留3位小数
    for (const auto& element : data) {
        ss << element;
        ss << ", ";
    }
    std::string row_str = ss.str();
    LOGI("%s", row_str.c_str());
}

void printf_1d_vector(const vector<double>& data)
{
    std::stringstream ss;
    ss << std::fixed << std::setprecision(printf_num); // 设置浮点数输出格式：保留3位小数
    for (const auto& element : data) {
        ss << element;
        ss << ", ";
    }
    std::string row_str = ss.str();
    LOGI("%s", row_str.c_str());
}

void analysis_mean_deque(const deque<double>& data, double& Vmin, double& Vmax, double& Vmean)
{
    double vec_sum = 0;
    Vmin = image_width;
    Vmax = -1;
    int vec_n = (int)data.size();
    for(auto& p : data)
    {
        vec_sum += p;
        // 最大值
        if(p > Vmax)
        {
            Vmax = p;
        }
        // 最小值
        if(p < Vmin)
        {
            Vmin = p;
        }
    }
    // 均值
    Vmean = vec_sum / (double)vec_n;
}

void analysis_mean_point(const vector<cv::Point>& data, Point& Pmean)
{
    int px = 0;
    int py = 0;
    int vec_n = (int)data.size();
    for(auto& p : data)
    {
        px += p.x;
        py += p.y;
    }

    Pmean = cv::Point((int) (px / vec_n), (int) (py / vec_n));
}


// 函数用来将数据写入文件
void saveDataToFile(const std::vector<std::vector<Point2f>>& data, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        LOGE("saveDataToFile:  Failed to open file: %s", filename.c_str());
        // Optionally, print out the error state of the file stream.
        std::ios_base::iostate err = file.rdstate();
        if (err & std::ios_base::failbit) {
            LOGE("File creation failed due to failbit being set.");
        }
        if (err & std::ios_base::badbit) {
            LOGE("File creation failed due to badbit being set.");
        }
        return;
    }

    for (const auto& row : data) {
        for (size_t i = 0; i < row.size(); ++i) {
            file << row[i].x << "," << row[i].y;
            if (i != row.size() - 1) {
                file << ","; // 分隔符
            }
        }
        file << "\n"; // 换行
    }
    file.close();
    LOGI("saveDataToFile:  Succeed to save file: %s", filename.c_str());
}

/*
 * 罗列指定路径的文件
 */
void ListFilesInDirectory(const char *dirName, std::vector<std::string>& filenameList)
{
    DIR *dir;
    struct dirent *entry;
    filenameList.clear();

    if ((dir = opendir(dirName)) != nullptr)
    {
        LOGI("utils.cpp, ListFilesInDirectory: ");
        while ((entry = readdir(dir)) != nullptr)
        {
            std::string filename = entry->d_name;
            LOGI("%s\n", filename.c_str());
            if (isSupportedType(filename))
            {
                filenameList.push_back(filename);
            }
        }
        closedir(dir);
    }
    else
    {
        LOGE("utils.cpp ListFilesInDirectory: Could not open directory!!!");
    }
}

// 辅助函数，用于检查文件是否具有指定的扩展名
bool isSupportedType(const std::string& filename) {
    std::string extension;
    size_t dotPos = filename.find_last_of('.');
    if (dotPos != std::string::npos) {
        extension = filename.substr(dotPos + 1);
    }

    std::string lowerExtension = extension;
    std::transform(lowerExtension.begin(), lowerExtension.end(), lowerExtension.begin(), ::tolower);

    return lowerExtension == "txt" || lowerExtension == "json" || lowerExtension == "avi";
}

float calculate_rmse(vector<vector<float>>& model_param, vector<vector<float>>& data_x, vector<vector<float>>& data_y)
{
    std::vector<float> params_x = model_param[0];
    std::vector<float> params_y = model_param[1];

    // --- 创建feature ---
    int n = (int)data_x.size(); // 数据点的数量
    int num_params = (int)data_x[0].size(); // 参数的数量

    // 创建设计矩阵和输出向量
    MatrixXf feature_x(n, num_params); // 自变量 + 1个截距项
    MatrixXf feature_y(n, num_params); // 自变量 + 1个截距项

    VectorXf result_x(n);             // 目标-x
    VectorXf result_y(n);             // 目标-y

    VectorXf coeffs_x(num_params);  // 权重参数 - x
    VectorXf coeffs_y(num_params);  // 权重参数 - y

    for (int i = 0; i < n; ++i) {
        // 填充设计矩阵
        for (int j = 0; j < num_params-1; ++j) {
            feature_x(i, j) = data_x[i][j];
            feature_y(i, j) = data_y[i][j];
        }
        feature_x(i, num_params-1) = 1.0f; // 截距项
        feature_y(i, num_params-1) = 1.0f; // 截距项

        result_x(i) = data_x[i][num_params-1]; // y 值
        result_y(i) = data_y[i][num_params-1]; // y 值
    }

    for (int j = 0; j < num_params; ++j)
    {
        coeffs_x(j) = params_x[j];
        coeffs_y(j) = params_y[j];
    }
    // ---------------------

    // 计算预测值
    VectorXf x_pred = feature_x * coeffs_x;
    VectorXf y_pred = feature_y * coeffs_y;

    // 计算残差
    VectorXf residuals_x = result_x - x_pred;
    VectorXf residuals_y = result_y - y_pred;
    residuals_x = residuals_x.cwiseAbs().array().square();
    residuals_y = residuals_y.cwiseAbs().array().square();

    VectorXf residuals = residuals_x + residuals_y;
    // 将值控制在0~1之间
    Eigen::VectorXf sqrtVec = residuals.array().sqrt().unaryExpr([](float v) {
        return std::min(std::max(v, 0.f), 1.f);
    });

    vector<float> sqrtVec_(sqrtVec.data(), sqrtVec.data() + sqrtVec.size());
    LOGE("utils.cpp calculate_rmse: printf sqrtVec.....");
    printf_1d_vector(sqrtVec_);
    float score = logistic_func(sqrtVec_) * (float)n / 9.0F;
    return score;
}

float logistic_func(vector<float>& input_data)
{
    float result = 0;
    float L = 1.16187482;
    float k = -20;
    float x0 = 0.10400553;

    auto n = (float)input_data.size(); // 数据点的数量

    for (auto inp: input_data)
    {
        result += L / (1 + expf(-k * (inp - x0)));
    }
    float rse = result / n;
    return rse;
}

// 函数用来将数据写入文件
void saveDataToFile(const std::vector<Point>& data, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        LOGE("saveDataToFile:  Failed to open file: %s", filename.c_str());
        // Optionally, print out the error state of the file stream.
        std::ios_base::iostate err = file.rdstate();
        if (err & std::ios_base::failbit) {
            LOGE("File creation failed due to failbit being set.");
        }
        if (err & std::ios_base::badbit) {
            LOGE("File creation failed due to badbit being set.");
        }
        return;
    }
    for (size_t i = 0; i < data.size(); ++i) {
        file << data[i].x << "," << data[i].y;
        if (i != data.size() - 1) {
            file << ","; // 分隔符
        }
    }
    file << "\n"; // 换行
    file.close();
    LOGI("saveDataToFile:  Succeed to save file: %s", filename.c_str());
}

// 函数定义
void boolVectorToDecimal(const bool boolVec[], const int& number, int& decimal_left, int& decimal_right) {
    for (int i = 0; i < number; ++i) {
        // 如果当前位是true(1)，则将其对应的2的幂加到decimal上
        if (boolVec[i*2]) {
            decimal_left |= 1 << i; // 注意：这里假设boolVec是从最高有效位开始的
        }
        if (boolVec[i*2 +1]) {
            decimal_right |= 1 << i; // 注意：这里假设boolVec是从最高有效位开始的
        }
    }
}

/* 将眼动轨迹的列表数据转化成json格式的string*/
string transfer_trajectory_to_jsondata(std::vector<std::vector<Point3f>>& stare_points)
{
    // 创建根对象
    Json::Value root;
    Json::Value pointsArray;

    int size = (int)stare_points.size();
    int grade = 1;
    // 计算每个注视点的中心点坐标以及等级
    for(int i = 0; i < size; i++)
    {
        Json::Value one_point;
        vector<Point3f>& points = stare_points[i];
        int num = (int)points.size();
        float sum_x = 0;
        float sum_y = 0;
        float sum_duration = 0;
        for(auto& point:points)
        {
            sum_x += point.x;
            sum_y += point.y;
            sum_duration += point.z;
        }
        float mean_x = sum_x / (float)num;
        float mean_y = sum_y / (float)num;

        one_point["index"] = i + 1;
        one_point["x"] = mean_x;
        one_point["y"] = mean_y;
        one_point["duration"] = sum_duration;
        pointsArray.append(one_point);
    }

    root["gaze"] = pointsArray;

    // 设置样式为紧凑格式或美观格式
    Json::FastWriter fastWriter;
    return fastWriter.write(root);
}
