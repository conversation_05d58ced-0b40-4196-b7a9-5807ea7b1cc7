#ifndef FACE_DET_H
#define FACE_DET_H

#include "utils.h"

#define face_det_net_w 224
#define face_det_net_h 160

#define eye_det_width_dist_min_threshold 300
#define eye_det_width_dist_max_threshold 1200
#define eye_det_height_dist_max_threshold 150

#define expand_width 30

#define face_det_threshold 0.6
#define default_rect_ratio 0.05

class faceDetect
{
public:
    faceDetect();
    ~faceDetect();
    void load_model(char* model_path, bool& model_isready);
    void recognize(const cv::Mat& src, cv::Rect& left_rect, cv::Rect& right_rect, bool& det_failed);

private:
    rknn_context model_ctx;
    rknn_input *data_inputs;
    rknn_output *outputs;
};

#endif //FACE_DET_H