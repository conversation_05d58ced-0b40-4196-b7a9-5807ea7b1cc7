//
// Created by user on 2024/12/17.
//

#include "saveVideos.h"

SaveVideos::SaveVideos(){
    prefix_name = "";
    params_matrix.clear();   // 清空保存参数列表
    left_save_iris_p = Point(setting_pose_left_eye_x, setting_pose_left_eye_y);    // 初始化左眼位置
    right_save_iris_p = Point(setting_pose_right_eye_x, setting_pose_right_eye_y); // 初始化右眼位置

    save_flag = false;
}

SaveVideos::~SaveVideos() {
    LOGI("------- destropy SaveVideos!!!! ------");
    close_videoCapture();
}

/**
 * 开始保存视频数据
 */
void SaveVideos::start_saving_videos(string& pre_name)
{
    prefix_name = pre_name;
    save_flag = true;
    params_matrix.clear();   // 清空保存参数列表
    create_videoCapture();
}

/**
 * 结束
 */
bool SaveVideos::stop_saving_videos(const calibration_params_data_str& calib_datas)
{
    bool state = false;
    if(save_flag)
    {
        save_flag = false;
        close_videoCapture();
        LOGI("SaveVideos::stop_saving_videos run close_videoCapture()!!!!");
        // --- 保存检测参数和校准参数 ---
        writeDebugParamsToFile(calib_debug_params_filename, calib_datas);  // 保存检测参数
        saveDataToFile(params_matrix, video_params_filename);   //  保存检测参数
        state = true;
    }
    return state;
}

/**
 * 根据检测结果保存视频数据
 */
void SaveVideos::save_videos_func(const cv::Mat &Image, const frame_detection_result& det_result)
{
    if(save_flag)
    {
        // 初始化要保存的参数列表
        vector<Point2f> left_eye_det_params(6, Point2f(-1.0F, -1.0F));    // 左眼检测数据：patch_cent, iris_det_tl, iris_det_br, left_p, right_p, pupil_cent
        vector<Point2f> right_eye_det_params(6, Point2f(-1.0F, -1.0F));   // 右眼检测数据：patch_cent, iris_det_tl. iris_det_br, left_p, right_p, pupil_cent
        // 单通道图像
        cv::Mat redChannel = Image.clone();
        // --- 保存人脸检测图像数据 ---
        face_videoWrite(redChannel);  //保存视频

        //  --- 保存虹膜检测结果 ---
        cv::Rect left_iris_rect = det_result.left_eye.iris_rect;
        cv::Rect right_iris_rect = det_result.right_eye.iris_rect;
        left_eye_det_params[1] = Point2f((float)left_iris_rect.x, (float)left_iris_rect.y);  // iris_det_tl
        left_eye_det_params[2] = Point2f((float)(left_iris_rect.x + left_iris_rect.width), (float)(left_iris_rect.y + left_iris_rect.height));  // iris_det_br
        right_eye_det_params[1] = Point2f((float)right_iris_rect.x, (float)right_iris_rect.y);  // iris_det_tl
        right_eye_det_params[2] = Point2f((float)(right_iris_rect.x + right_iris_rect.width), (float)(right_iris_rect.y + right_iris_rect.height));  // iris_det_br
        // ----------------------

        //  --- 保存光斑和瞳孔检测结果 ---
        left_eye_det_params[3] = det_result.left_eye.light_left;  // left_p
        left_eye_det_params[4] = det_result.left_eye.light_right;  // right_p
        left_eye_det_params[5] = det_result.left_eye.pupil;  // pupil_cent

        right_eye_det_params[3] = det_result.right_eye.light_left;  // left_p
        right_eye_det_params[4] = det_result.right_eye.light_right;  // right_p
        right_eye_det_params[5] = det_result.right_eye.pupil;  // pupil_cent
        // ----------------------

        // 保存眼睛区域的视频数据和检测参数
        if(left_iris_rect.width > 0 && left_iris_rect.height > 0)
        {
            left_save_iris_p.x = (int)(left_iris_rect.x + left_iris_rect.width / 2);
            left_save_iris_p.y = (int)(left_iris_rect.y + left_iris_rect.height / 2);
        }
        if(right_iris_rect.width > 0 && right_iris_rect.height > 0)
        {
            right_save_iris_p.x = (int)(right_iris_rect.x + right_iris_rect.width / 2);
            right_save_iris_p.y = (int)(right_iris_rect.y + right_iris_rect.height / 2);
        }

        left_videoWrite(redChannel, left_save_iris_p);    //保存左眼视频
        right_videoWrite(redChannel, right_save_iris_p);  //保存右眼视频

        left_eye_det_params[0] = left_save_iris_p;
        right_eye_det_params[0] = right_save_iris_p;

        params_matrix.push_back(left_eye_det_params);   // params of left eye
        params_matrix.push_back(right_eye_det_params);  // params of right eye
    }
}

/**
 * 创建视频文件名
 */
void SaveVideos::create_video_filename()
{
    strcpy(face_video_filename, prefix_name.c_str());
    strcpy(left_video_filename,prefix_name.c_str());
    strcpy(right_video_filename,prefix_name.c_str());
    strcpy(video_params_filename,prefix_name.c_str());
    strcpy(calib_debug_params_filename,prefix_name.c_str());

    const int face_len = (int)strlen(face_video_filename);
    const int left_len = (int)strlen(left_video_filename);
    const int right_len = (int)strlen(right_video_filename);
    const int base_len = (int)strlen(video_params_filename);
    const int debug_len = (int)strlen(calib_debug_params_filename);

    sprintf(face_video_filename + face_len, "_face.avi");
    sprintf(left_video_filename + left_len, "_Leye.avi");
    sprintf(right_video_filename + right_len, "_Reye.avi");
    sprintf(video_params_filename + base_len, "_detection.txt");
    sprintf(calib_debug_params_filename + debug_len, "_params.json");
    LOGI("video_filename is %s!!!!", face_video_filename);
}

/**
 * 打开新的视频数据
 */
void SaveVideos::create_videoCapture()
{
    // 重新开始新的视频数据
    close_videoCapture();   // 先关闭和初始化视频数据

    create_video_filename();   //创建视频文件名
    face_outputVideo.open(face_video_filename, cv::VideoWriter::fourcc('M','J','P','G'), 30, Size(video_face_img_w, video_face_img_h));
    left_outputVideo.open(left_video_filename, cv::VideoWriter::fourcc('M','J','P','G'), 30, Size(video_eye_img_w, video_eye_img_h));
    right_outputVideo.open(right_video_filename, cv::VideoWriter::fourcc('M','J','P','G'), 30, Size(video_eye_img_w, video_eye_img_h));
    if(!face_outputVideo.isOpened() || !left_outputVideo.isOpened() || !right_outputVideo.isOpened())
    {
        LOGI("video_filename open failed!!!!");
    }
    else
    {
        LOGI("video_filename open succeed!!!!");
    }
}

/**
 * 关闭视频数据
 */
void SaveVideos::close_videoCapture()
{
    // 重新开始新的视频数据
    if(face_outputVideo.isOpened())
    {
        face_outputVideo.release();  //关闭视频
    }
    if(left_outputVideo.isOpened())
    {
        left_outputVideo.release();  //关闭视频
    }
    if(right_outputVideo.isOpened())
    {
        right_outputVideo.release();  //关闭视频
    }
    if(right_outputVideo.isOpened())
    {
        right_outputVideo.release();  //关闭视频
    }
    left_save_iris_p = Point(setting_pose_left_eye_x, setting_pose_left_eye_y);     // 初始化左眼位置
    right_save_iris_p = Point(setting_pose_right_eye_x, setting_pose_right_eye_y);  // 初始化右眼位置
}

void SaveVideos::face_videoWrite(Mat& image)
{
    Mat save_video_img;
    resize(image, save_video_img, Size(video_face_img_w, video_face_img_h));
    // 创建一个目标图像
    cv::Mat bgrImage;
    // 使用cvtColor函数将灰度图像转换为BGR图像
    cv::cvtColor(save_video_img, bgrImage, cv::COLOR_GRAY2BGR);
    face_outputVideo.write(bgrImage);
}
void SaveVideos::left_videoWrite(Mat& image, Point& iris_cent)
{
    Rect save_rect;
    save_rect.x = max(0, (int)(iris_cent.x - video_eye_img_w / 2));
    if (save_rect.x >= image.cols - video_eye_img_w)
    {
        save_rect.x = image.cols - video_eye_img_w - 1;
    }

    save_rect.y = max(0, (int)(iris_cent.y - video_eye_img_h / 2));
    if (save_rect.y >= image.rows - video_eye_img_h)
    {
        save_rect.y = image.rows - video_eye_img_h - 1;
    }

    save_rect.width = video_eye_img_w;
    save_rect.height = video_eye_img_h;

    Mat save_video_img = image(save_rect);

    iris_cent.x = (int)(save_rect.x + video_eye_img_w / 2);
    iris_cent.y = (int)(save_rect.y + video_eye_img_h / 2);

    // 创建一个目标图像
    cv::Mat bgrImage;
    // 使用cvtColor函数将灰度图像转换为BGR图像
    cv::cvtColor(save_video_img, bgrImage, cv::COLOR_GRAY2BGR);
    left_outputVideo.write(bgrImage);
}
void SaveVideos::right_videoWrite(Mat& image, Point& iris_cent)
{
    Rect save_rect;
    save_rect.x = max(0, (int)(iris_cent.x - video_eye_img_w / 2));
    if (save_rect.x >= image.cols - video_eye_img_w)
    {
        save_rect.x = image.cols - video_eye_img_w - 1;
    }

    save_rect.y = max(0, (int)(iris_cent.y - video_eye_img_h / 2));
    if (save_rect.y >= image.rows - video_eye_img_h)
    {
        save_rect.y = image.rows - video_eye_img_h - 1;
    }

    save_rect.width = video_eye_img_w;
    save_rect.height = video_eye_img_h;

    Mat save_video_img = image(save_rect);

    iris_cent.x = (int)(save_rect.x + video_eye_img_w / 2);
    iris_cent.y = (int)(save_rect.y + video_eye_img_h / 2);

    // 创建一个目标图像
    cv::Mat bgrImage;
    // 使用cvtColor函数将灰度图像转换为BGR图像
    cv::cvtColor(save_video_img, bgrImage, cv::COLOR_GRAY2BGR);
    right_outputVideo.write(bgrImage);
}