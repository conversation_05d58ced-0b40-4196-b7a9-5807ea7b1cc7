<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2d3748; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #4a5568; }
      .text { font-family: Arial, sans-serif; font-size: 14px; fill: #2d3748; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; fill: #4a5568; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; fill: #2d3748; }
      .arrow { stroke: #4a5568; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #3182ce; stroke-width: 3; fill: none; marker-end: url(#bluearrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4a5568"/>
    </marker>
    <marker id="bluearrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3182ce"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">协程作用域、线程切换与实际应用场景</text>

  <!-- 什么是协程 -->
  <rect x="50" y="60" width="500" height="180" fill="#f7fafc" stroke="#4299e1" stroke-width="2" rx="8"/>
  <text x="300" y="85" text-anchor="middle" class="subtitle">什么是协程 (Coroutine)</text>
  
  <text x="70" y="110" class="text">协程是一种轻量级的并发编程模型:</text>
  <text x="90" y="130" class="small-text">• 比线程更轻量，创建成本低</text>
  <text x="90" y="145" class="small-text">• 协作式多任务，非抢占式调度</text>
  <text x="90" y="160" class="small-text">• 可以在执行过程中暂停和恢复</text>
  <text x="90" y="175" class="small-text">• 通过 suspend 函数实现异步操作</text>
  <text x="90" y="190" class="small-text">• 避免回调地狱，代码更简洁</text>
  <text x="90" y="205" class="small-text">• 结构化并发，易于管理生命周期</text>
  <text x="90" y="220" class="small-text">• 内置异常处理和取消机制</text>

  <!-- 协程作用域 -->
  <rect x="580" y="60" width="500" height="180" fill="#f0fff4" stroke="#48bb78" stroke-width="2" rx="8"/>
  <text x="830" y="85" text-anchor="middle" class="subtitle">协程作用域 (CoroutineScope)</text>
  
  <text x="600" y="110" class="text">作用域定义协程的生命周期边界:</text>
  <text x="620" y="130" class="small-text">• GlobalScope: 应用程序级别，慎用</text>
  <text x="620" y="145" class="small-text">• lifecycleScope: Activity/Fragment 生命周期</text>
  <text x="620" y="160" class="small-text">• viewModelScope: ViewModel 生命周期</text>
  <text x="620" y="175" class="small-text">• coroutineScope: 自定义作用域</text>
  <text x="620" y="190" class="small-text">• supervisorScope: 子协程异常隔离</text>
  <text x="620" y="205" class="small-text">• runBlocking: 阻塞当前线程</text>
  <text x="620" y="220" class="small-text">• 作用域销毁时自动取消所有子协程</text>

  <!-- 协程调度器 -->
  <rect x="1110" y="60" width="440" height="180" fill="#fffaf0" stroke="#ed8936" stroke-width="2" rx="8"/>
  <text x="1330" y="85" text-anchor="middle" class="subtitle">协程调度器 (Dispatcher)</text>
  
  <text x="1130" y="110" class="text">调度器决定协程在哪个线程执行:</text>
  <text x="1150" y="130" class="small-text">• Dispatchers.Main: UI 线程</text>
  <text x="1150" y="145" class="small-text">• Dispatchers.IO: I/O 密集型任务</text>
  <text x="1150" y="160" class="small-text">• Dispatchers.Default: CPU 密集型任务</text>
  <text x="1150" y="175" class="small-text">• Dispatchers.Unconfined: 不限制线程</text>
  <text x="1150" y="190" class="small-text">• 自定义线程池调度器</text>
  <text x="1150" y="205" class="small-text">• withContext() 切换调度器</text>
  <text x="1150" y="220" class="small-text">• async/await 并发执行</text>

  <!-- 线程切换流程图 -->
  <rect x="50" y="270" width="1500" height="300" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="8"/>
  <text x="800" y="295" text-anchor="middle" class="subtitle">协程线程切换流程</text>

  <!-- Main Thread -->
  <rect x="80" y="320" width="120" height="60" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="4"/>
  <text x="140" y="340" text-anchor="middle" class="text">Main Thread</text>
  <text x="140" y="355" text-anchor="middle" class="small-text">(UI 操作)</text>
  <text x="140" y="370" text-anchor="middle" class="code-text">Dispatchers.Main</text>

  <!-- IO Thread -->
  <rect x="280" y="320" width="120" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="4"/>
  <text x="340" y="340" text-anchor="middle" class="text">IO Thread</text>
  <text x="340" y="355" text-anchor="middle" class="small-text">(网络/数据库)</text>
  <text x="340" y="370" text-anchor="middle" class="code-text">Dispatchers.IO</text>

  <!-- Default Thread -->
  <rect x="480" y="320" width="120" height="60" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="4"/>
  <text x="540" y="340" text-anchor="middle" class="text">Default Thread</text>
  <text x="540" y="355" text-anchor="middle" class="small-text">(CPU 密集)</text>
  <text x="540" y="370" text-anchor="middle" class="code-text">Dispatchers.Default</text>

  <!-- 切换箭头 -->
  <line x1="200" y1="350" x2="280" y2="350" class="flow-arrow"/>
  <text x="240" y="345" text-anchor="middle" class="small-text">withContext(IO)</text>

  <line x1="400" y1="350" x2="480" y2="350" class="flow-arrow"/>
  <text x="440" y="345" text-anchor="middle" class="small-text">withContext(Default)</text>

  <line x1="540" y1="320" x2="340" y2="280" class="flow-arrow"/>
  <text x="440" y="295" text-anchor="middle" class="small-text">withContext(IO)</text>

  <line x1="340" y1="320" x2="140" y2="280" class="flow-arrow"/>
  <text x="240" y="295" text-anchor="middle" class="small-text">withContext(Main)</text>

  <!-- 代码示例 -->
  <rect x="650" y="320" width="880" height="240" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="4"/>
  <text x="1090" y="340" text-anchor="middle" class="subtitle">线程切换代码示例</text>
  
  <text x="670" y="365" class="code-text">lifecycleScope.launch {</text>
  <text x="690" y="380" class="code-text">    // 1. 在 Main 线程更新 UI</text>
  <text x="690" y="395" class="code-text">    progressBar.visibility = View.VISIBLE</text>
  <text x="690" y="410" class="code-text">    </text>
  <text x="690" y="425" class="code-text">    // 2. 切换到 IO 线程执行网络请求</text>
  <text x="690" y="440" class="code-text">    val result = withContext(Dispatchers.IO) {</text>
  <text x="710" y="455" class="code-text">        apiService.fetchData() // 网络请求</text>
  <text x="690" y="470" class="code-text">    }</text>
  <text x="690" y="485" class="code-text">    </text>
  <text x="690" y="500" class="code-text">    // 3. 切换到 Default 线程处理数据</text>
  <text x="690" y="515" class="code-text">    val processedData = withContext(Dispatchers.Default) {</text>
  <text x="710" y="530" class="code-text">        processLargeData(result) // CPU 密集型操作</text>
  <text x="690" y="545" class="code-text">    }</text>
  <text x="690" y="560" class="code-text">    </text>
  <text x="670" y="575" class="code-text">}</text>

  <!-- 实际应用场景 -->
  <text x="800" y="620" text-anchor="middle" class="title">实际应用场景</text>

  <!-- 场景1: 图片加载 -->
  <rect x="50" y="650" width="350" height="200" fill="#f0f8ff" stroke="#4169e1" stroke-width="2" rx="8"/>
  <text x="225" y="675" text-anchor="middle" class="subtitle">场景1: 图片加载与缓存</text>

  <text x="70" y="700" class="code-text">viewModelScope.launch {</text>
  <text x="90" y="715" class="code-text">  // UI线程显示加载状态</text>
  <text x="90" y="730" class="code-text">  _loading.value = true</text>
  <text x="90" y="745" class="code-text">  </text>
  <text x="90" y="760" class="code-text">  val bitmap = withContext(IO) {</text>
  <text x="110" y="775" class="code-text">    // IO线程下载图片</text>
  <text x="110" y="790" class="code-text">    downloadImage(url)</text>
  <text x="90" y="805" class="code-text">  }</text>
  <text x="90" y="820" class="code-text">  </text>
  <text x="90" y="835" class="code-text">  // 回到UI线程更新界面</text>
  <text x="90" y="850" class="code-text">  imageView.setImageBitmap(bitmap)</text>
  <text x="70" y="865" class="code-text">}</text>

  <!-- 场景2: 数据库操作 -->
  <rect x="420" y="650" width="350" height="200" fill="#f0fff0" stroke="#32cd32" stroke-width="2" rx="8"/>
  <text x="595" y="675" text-anchor="middle" class="subtitle">场景2: 数据库操作</text>

  <text x="440" y="700" class="code-text">lifecycleScope.launch {</text>
  <text x="460" y="715" class="code-text">  // UI线程显示进度</text>
  <text x="460" y="730" class="code-text">  showProgress()</text>
  <text x="460" y="745" class="code-text">  </text>
  <text x="460" y="760" class="code-text">  val users = withContext(IO) {</text>
  <text x="480" y="775" class="code-text">    // IO线程查询数据库</text>
  <text x="480" y="790" class="code-text">    userDao.getAllUsers()</text>
  <text x="460" y="805" class="code-text">  }</text>
  <text x="460" y="820" class="code-text">  </text>
  <text x="460" y="835" class="code-text">  // UI线程更新列表</text>
  <text x="460" y="850" class="code-text">  adapter.updateUsers(users)</text>
  <text x="440" y="865" class="code-text">}</text>

  <!-- 场景3: 并发网络请求 -->
  <rect x="790" y="650" width="350" height="200" fill="#fff8dc" stroke="#daa520" stroke-width="2" rx="8"/>
  <text x="965" y="675" text-anchor="middle" class="subtitle">场景3: 并发网络请求</text>

  <text x="810" y="700" class="code-text">viewModelScope.launch {</text>
  <text x="830" y="715" class="code-text">  val deferred1 = async(IO) {</text>
  <text x="850" y="730" class="code-text">    api.getUserInfo()</text>
  <text x="830" y="745" class="code-text">  }</text>
  <text x="830" y="760" class="code-text">  val deferred2 = async(IO) {</text>
  <text x="850" y="775" class="code-text">    api.getUserPosts()</text>
  <text x="830" y="790" class="code-text">  }</text>
  <text x="830" y="805" class="code-text">  </text>
  <text x="830" y="820" class="code-text">  // 等待所有请求完成</text>
  <text x="830" y="835" class="code-text">  val user = deferred1.await()</text>
  <text x="830" y="850" class="code-text">  val posts = deferred2.await()</text>
  <text x="810" y="865" class="code-text">}</text>

  <!-- 场景4: 定时任务 -->
  <rect x="1160" y="650" width="350" height="200" fill="#ffe4e1" stroke="#dc143c" stroke-width="2" rx="8"/>
  <text x="1335" y="675" text-anchor="middle" class="subtitle">场景4: 定时任务与轮询</text>

  <text x="1180" y="700" class="code-text">lifecycleScope.launch {</text>
  <text x="1200" y="715" class="code-text">  while (isActive) {</text>
  <text x="1220" y="730" class="code-text">    val data = withContext(IO) {</text>
  <text x="1240" y="745" class="code-text">      // 获取最新数据</text>
  <text x="1240" y="760" class="code-text">      fetchLatestData()</text>
  <text x="1220" y="775" class="code-text">    }</text>
  <text x="1220" y="790" class="code-text">    </text>
  <text x="1220" y="805" class="code-text">    // UI线程更新界面</text>
  <text x="1220" y="820" class="code-text">    updateUI(data)</text>
  <text x="1220" y="835" class="code-text">    </text>
  <text x="1220" y="850" class="code-text">    delay(5000) // 5秒间隔</text>
  <text x="1200" y="865" class="code-text">  }</text>
  <text x="1180" y="880" class="code-text">}</text>

  <!-- 协程优势总结 -->
  <rect x="50" y="900" width="1500" height="120" fill="#f5f5f5" stroke="#666666" stroke-width="2" rx="8"/>
  <text x="800" y="925" text-anchor="middle" class="subtitle">协程的核心优势</text>

  <text x="80" y="950" class="text">1. 轻量级并发: 一个线程可以运行成千上万个协程</text>
  <text x="80" y="970" class="text">2. 结构化并发: 作用域自动管理协程生命周期，防止内存泄漏</text>
  <text x="80" y="990" class="text">3. 异常处理: 统一的异常传播机制，易于调试和处理</text>
  <text x="80" y="1010" class="text">4. 取消机制: 支持协作式取消，可以优雅地停止长时间运行的任务</text>

  <text x="800" y="950" class="text">5. 线程安全: 通过调度器确保线程安全的上下文切换</text>
  <text x="800" y="970" class="text">6. 代码简洁: 避免回调地狱，使异步代码看起来像同步代码</text>
  <text x="800" y="990" class="text">7. 性能优化: 减少线程创建和切换的开销</text>
  <text x="800" y="1010" class="text">8. 易于测试: 支持测试调度器，便于单元测试</text>

  <!-- 最佳实践 -->
  <rect x="50" y="1040" width="1500" height="120" fill="#e6f3ff" stroke="#0066cc" stroke-width="2" rx="8"/>
  <text x="800" y="1065" text-anchor="middle" class="subtitle">协程最佳实践</text>

  <text x="80" y="1090" class="text">• 使用合适的作用域: Activity/Fragment 用 lifecycleScope，ViewModel 用 viewModelScope</text>
  <text x="80" y="1110" class="text">• 选择正确的调度器: UI 操作用 Main，I/O 操作用 IO，CPU 密集型用 Default</text>
  <text x="80" y="1130" class="text">• 避免使用 GlobalScope: 除非确实需要应用程序级别的协程</text>
  <text x="80" y="1150" class="text">• 使用 supervisorScope: 当需要子协程异常隔离时</text>
