//
// Created by knight on 2023/6/9.
//
#include "kalmanfilter.h"

CoordinateKmf::CoordinateKmf(int state_num, int measure_num){
    //1.kalman filter setup
    stateNum = state_num;
    measureNum = measure_num;
    RNG rng;
    KF = new cv::KalmanFilter(stateNum, measureNum, 0);
    KF->transitionMatrix = (Mat_<float>(4, 4) <<1,0,1,0,0,1,0,1,0,0,1,0,0,0,0,1);  //转移矩阵A
    setIdentity(KF->measurementMatrix);                                             //测量矩阵H  (setIdentity: I)
    setIdentity(KF->processNoiseCov, Scalar::all(1e-1));                            //系统噪声方差矩阵Q
    setIdentity(KF->measurementNoiseCov, Scalar::all(5e-1));                        //测量噪声方差矩阵R
    setIdentity(KF->errorCovPost, Scalar::all(1));                                  //后验错误估计协方差矩阵P
    rng.fill(KF->statePost,RNG::UNIFORM,0,1.0);   //初始状态值x(0)
    measurement = Mat::zeros(measureNum, 1, CV_32F);                                //初始测量值x'(0)，因为后面要更新这个值，所以必须先定义
}

CoordinateKmf::~CoordinateKmf() {}

void CoordinateKmf::update(float x, float y){
    // 2.kalman prediction
    Mat prediction = KF->predict();
    predict_pt = Point2f(prediction.at<float>(0),prediction.at<float>(1) );   //预测值(x',y')

    //3.update measurement
    measurement.at<float>(0) = x;
    measurement.at<float>(1) = y;

    //4.update
    KF->correct(measurement);
}