//
// Created by knight on 2024/12/4.
//

#ifndef MIT_DD_DEMO_READING_H
#define MIT_DD_DEMO_READING_H

#include "utils.h"

using namespace cv;
using namespace std;

class ReadingPractice {
public:
    ReadingPractice();
    ~ReadingPractice();

public:
    std::vector<cv::Point3f> gaze_data_list;    // 应用期间采集的眼动数据

public:
    void reset_params_func();                                // 清空缓存数据
    void collect_data(float x, float y, float duration);     // 应用期间传输数据
    string get_record_jsondata();                            // 获取json的数据
private:
    std::vector<std::vector<Point3f>> process_stare_points();                            // 处理记录的眼动数据，转化成阅读训练数据
};



#endif //MIT_DD_DEMO_READING_H
