package com.airdoc.mpd.ppg.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: TimeDomainParameters
 * Author by lilin,Date on 2025/6/20 16:20
 * PS: Not easy to write code, please indicate.
 * 时域参数数据类
 */
@Parcelize
data class TimeDomainParameters(
    // 平均RR间期
    val meanNN: Double,
    // RR间期标准差
    val sdnn: Double,
    // 相邻RR间期差值的均方根
    val rmssd: Double,
    // 相邻RR间期差值的标准差
    val sdsd: Double,
    // 相邻RR间期差值>50ms的比例
    val pnn50: Double
): Parcelable
