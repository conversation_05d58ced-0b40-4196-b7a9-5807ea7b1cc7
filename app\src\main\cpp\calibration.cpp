//
// Created by knight on 2023/7/21.
//

#include "calibration.h"

/**
 * calibration each eye
 */
Eye_state::Eye_state(int* idx_list, float(*target_points)[2])
{
    point_index_order = idx_list;
    calibrate_points = target_points;
    consist_num = 0;
    not_finish_capture_data = true;
    had_compute_flag = false;
}
Eye_state::~Eye_state() = default;

void Eye_state::reset_calib_parameter() {
    pre_det_points = keypoint_parameter();
    consist_num = 0;
    calib_params_data_vec.clear();
    calib_points_data_vec.clear();
    not_finish_capture_data = true;
    had_compute_flag = false;
}

void Eye_state::get_calib_original_params_func(vector<vector<double>>& params_vec)
{
    params_vec.clear();
    int num_size = (int)calib_params_data_vec.size();

    for (int idx = 0; idx < num_size; idx++) {
        keypoint_parameter feature = calib_params_data_vec[idx];
        screen_point target_p = calib_points_data_vec[idx];
        vector<double> temp = {(double)feature.pupil_x, (double)feature.pupil_y,
                               (double)feature.light_p_lx, (double)feature.light_p_ly,
                               (double)feature.light_p_rx, (double)feature.light_p_ry,
                               (double)feature.eye_dist, (double)target_p.screen_x, (double)target_p.screen_y};
        params_vec.push_back(temp);
    }
}

void Eye_state::get_computed_params_flag(bool& finish_flag) const
{
    finish_flag = had_compute_flag;
}

void Eye_state::set_finished_capture_flag()
{
    not_finish_capture_data = false;
    had_compute_flag = true;
}

void Eye_state::compare_calib_parameter_consist(const cv::Point2f& pupil_cent, const vector<cv::Point2f>& point_crs, float eye_dist, bool& finish_one_point_flag,
                                                bool& data_isready, int& return_consist_num, int valid_num, int point_index, int light_num_thres){
    if(not_finish_capture_data)
    {
        finish_one_point_flag = false;
        data_isready = false;
        if (pupil_cent.x > 0 && pupil_cent.y > 0 && eye_dist > 0 && valid_num >= light_num_thres)
        {
            keypoint_parameter current_points(pupil_cent, point_crs[0], point_crs[1], eye_dist);
            if (consist_num > 0)
            {
                float move_dist = current_points - pre_det_points;
                if (move_dist < point_move_threshold * (valid_num + 1))
                {
                    consist_num ++;
                }
                else
                {
                    consist_num = 1;
                }
            }
            else
            {
                consist_num = 1;
            }
            pre_det_points = current_points;
        }
        else
        {
            consist_num = 0;
            pre_det_points = keypoint_parameter();
        }

        return_consist_num = consist_num;

        // ======= finish one points ======
        if (consist_num >= consist_num_threshold)
        {
            LOGI("Eye_state::compare_calib_parameter_consist: finish one point!!!!!!!!!!!!!!");
            finish_one_point_flag = true;
            if (calib_params_data_vec.size() < calibration_number)
            {
                calib_params_data_vec.push_back(pre_det_points);
                screen_point current_target_p;
                current_target_p.screen_x = calibrate_points[point_index][0];
                current_target_p.screen_y = calibrate_points[point_index][1];
                calib_points_data_vec.push_back(current_target_p);
                consist_num = 0;
                pre_det_points = keypoint_parameter();
                LOGI("Eye_state::compare_calib_parameter_consist: capture keypoint parameter succeed!!!!!!!!!!!!!!");
            }
            if (point_index == point_index_order[calibration_number-1])
            {
                data_isready = true;
                not_finish_capture_data = false;
            }
        }
    }
    else
    {
        return_consist_num = consist_num;
        finish_one_point_flag = true;
        data_isready = true;
    }
}

/*
 data的最后一列保存y值
 num_params是输入的X的参数个数
 返回 系数值 和 截距值
 */
vector<float> Eye_state::solveLeastSquares(const vector<vector<float>>& data) {
    int n = (int)data.size(); // 数据点的数量
    int num_params = (int)data[0].size(); // 参数的数量

    LOGI("------- Eye_state::solveLeastSquares: shape: (h: %d, w: %d)------", n, num_params);
    LOGI("------- Eye_state::solveLeastSquares: data------");
    printf_2d_vector(data);

    // 创建设计矩阵和输出向量
    MatrixXf A(n, num_params); // 自变量 + 1个截距项
    VectorXf b(n);

    for (int i = 0; i < n; ++i) {
        // 填充设计矩阵
        for (int j = 0; j < num_params-1; ++j) {
            A(i, j) = data[i][j];
        }
        A(i, num_params-1) = 1.0f; // 截距项
        b(i) = data[i][num_params-1]; // y 值
    }

    // 使用正规方程解最小二乘问题
    VectorXf coeffs = A.householderQr().solve(b);

    // 将 Eigen::Vector 转换为 std::vector
    vector<float> result(coeffs.data(), coeffs.data() + coeffs.size());
    LOGI("------- Eye_state::solveLeastSquares: result ------");
    printf_1d_vector(result);
    return result;
}

/*
将校准采集的关键点数据转化成feature向量
 */
void Eye_state::calculate_feature_vector_func(vector<vector<float>>& crs_vector_x, vector<vector<float>>& left_vector_x, vector<vector<float>>& right_vector_x,
                                              vector<vector<float>>& crs_vector_y, vector<vector<float>>& left_vector_y, vector<vector<float>>& right_vector_y)
{
    crs_vector_x.clear();
    left_vector_x.clear();
    right_vector_x.clear();
    crs_vector_y.clear();
    left_vector_y.clear();
    right_vector_y.clear();

    int num_size = (int)calib_params_data_vec.size();

    for (int idx = 0; idx < num_size; idx++)
    {
        keypoint_parameter feature = calib_params_data_vec[idx];
        screen_point target_p = calib_points_data_vec[idx];
        if(feature.pupil_x > 0 && feature.pupil_y > 0)
        {
            // left point feature
            if (feature.light_p_lx > 0 && feature.light_p_ly > 0 && feature.eye_dist > 0)
            {
                float l_feat_0 = (feature.light_p_lx - feature.pupil_x) / feature.eye_dist;
                float l_feat_1 = (feature.light_p_ly - feature.pupil_y) / feature.eye_dist;
                vector<float> tmp_x = {l_feat_0, l_feat_1, l_feat_0 * l_feat_0, target_p.screen_x};
                vector<float> tmp_y = {l_feat_0, l_feat_1, l_feat_1 * l_feat_1, target_p.screen_y};
                left_vector_x.push_back(tmp_x);
                left_vector_y.push_back(tmp_y);
            }

            // right point feature
            if (feature.light_p_rx > 0 && feature.light_p_ry > 0 && feature.eye_dist > 0)
            {
                float r_feat_0 = (feature.light_p_rx - feature.pupil_x) / feature.eye_dist;
                float r_feat_1 = (feature.light_p_ry - feature.pupil_y) / feature.eye_dist;
                vector<float> tmp_x = {r_feat_0, r_feat_1, r_feat_0 * r_feat_0, target_p.screen_x};
                vector<float> tmp_y = {r_feat_0, r_feat_1, r_feat_1 * r_feat_1, target_p.screen_y};

                right_vector_x.push_back(tmp_x);
                right_vector_y.push_back(tmp_y);
            }

            // center point feature
            if (feature.light_p_lx > 0 && feature.light_p_ly > 0 && feature.light_p_rx > 0 && feature.light_p_ry > 0)
            {
                float crs_x = (feature.light_p_lx + feature.light_p_rx) / 2.0f;
                float crs_y = (feature.light_p_ly + feature.light_p_ry) / 2.0f;

                float dist = sqrtf(powf(feature.light_p_rx - feature.light_p_lx,2) + powf(feature.light_p_ry - feature.light_p_ly,2));

                float g_feat_0 = crs_x - feature.pupil_x;
                float g_feat_1 = crs_y - feature.pupil_y;
                float norm_g_feat_0 = g_feat_0 / dist;
                float norm_g_feat_1 = g_feat_1 / dist;
                vector<float> tmp_x = {norm_g_feat_0, norm_g_feat_1, norm_g_feat_0 * norm_g_feat_0, target_p.screen_x};
                vector<float> tmp_y = {norm_g_feat_0, norm_g_feat_1, norm_g_feat_1 * norm_g_feat_1, target_p.screen_y};
                crs_vector_x.push_back(tmp_x);
                crs_vector_y.push_back(tmp_y);
            }
        }
    }
}

void Eye_state::compute_Matrix_func(vector<vector<float>>& warpMatrix_data, float& score)
{
    warpMatrix_data.clear();
    if(!had_compute_flag)
    {
        // data is ready
        vector<vector<float>> crs_feat_vec_x;
        vector<vector<float>> crs_feat_vec_y;
        vector<vector<float>> left_feat_vec_x;
        vector<vector<float>> left_feat_vec_y;
        vector<vector<float>> right_feat_vec_x;
        vector<vector<float>> right_feat_vec_y;

        vector<vector<float>> warpMatrix_C;
        vector<vector<float>> warpMatrix_L;
        vector<vector<float>> warpMatrix_R;

        score = 0.0;

        calculate_feature_vector_func(crs_feat_vec_x, left_feat_vec_x, right_feat_vec_x, crs_feat_vec_y, left_feat_vec_y, right_feat_vec_y);
        // center
        if(crs_feat_vec_x.size() >= num_capture_params_min && crs_feat_vec_y.size() >= num_capture_params_min)
        {
            LOGI("------- Eye_state::compute_Matrix_func: computing result_C_x and result_C_y ... ------");
            vector<float> result_C_x = solveLeastSquares(crs_feat_vec_x);
            vector<float> result_C_y = solveLeastSquares(crs_feat_vec_y);
            warpMatrix_C = {result_C_x, result_C_y};
            // calculate calibration score
            score = calculate_rmse(warpMatrix_C, crs_feat_vec_x, crs_feat_vec_y);
            LOGI("------- Eye_state::compute_Matrix_func: center score is: %f ------", score);
        }
        else
        {
            LOGD("------- Eye_state::compute_Matrix_func: feature of crs is not enough (%d, %d)... ------", (int)crs_feat_vec_x.size(), (int)crs_feat_vec_y.size());
        }
        // left
        if(left_feat_vec_x.size() >= num_capture_params_min && left_feat_vec_y.size() >= num_capture_params_min)
        {
            LOGI("------- Eye_state::compute_Matrix_func: computing result_L_x and result_L_y ... ------");
            vector<float> result_L_x = solveLeastSquares(left_feat_vec_x);
            vector<float> result_L_y = solveLeastSquares(left_feat_vec_y);
            warpMatrix_L = {result_L_x, result_L_y};
        }
        else
        {
            LOGD("------- Eye_state::compute_Matrix_func: feature of left point is not enough  (%d, %d)... ------", (int)left_feat_vec_x.size(), (int)left_feat_vec_y.size());
        }
        // right
        if(right_feat_vec_x.size() >= num_capture_params_min && right_feat_vec_y.size() >= num_capture_params_min)
        {
            LOGI("------- Eye_state::compute_Matrix_func: computing result_R_x and result_R_y ... ------");
            vector<float> result_R_x = solveLeastSquares(right_feat_vec_x);
            vector<float> result_R_y = solveLeastSquares(right_feat_vec_y);
            warpMatrix_R = {result_R_x, result_R_y};
        }
        else
        {
            LOGD("------- Eye_state::compute_Matrix_func: feature of right point is not enough (%d, %d)... ------", (int)right_feat_vec_x.size(), (int)right_feat_vec_y.size());
        }
        if(warpMatrix_C.size() == 2 && warpMatrix_L.size() == 2 && warpMatrix_R.size() == 2)
        {
            LOGI("compute_Matrix_func: succeed to compute params.");
            warpMatrix_data.insert(warpMatrix_data.end(), warpMatrix_C.begin(), warpMatrix_C.end());
            warpMatrix_data.insert(warpMatrix_data.end(), warpMatrix_L.begin(), warpMatrix_L.end());
            warpMatrix_data.insert(warpMatrix_data.end(), warpMatrix_R.begin(), warpMatrix_R.end());
            had_compute_flag = true;
        }
        else
        {
            LOGD("compute_Matrix_func: failed to compute params. valid data is not enough  (%d, %d, %d)!", (int)warpMatrix_C.size(), (int)warpMatrix_L.size(), (int)warpMatrix_R.size());
        }
    }
    else
    {
        LOGD("compute_Matrix_func: data is not ready. check the code process.");
    }
}

Calibrator::Calibrator(int* idx_list, float(*target_points)[2]): calib_left_class(idx_list, target_points), calib_right_class(idx_list, target_points) {
    point_index_order = idx_list;
    for(int i = 0; i< 9; i++)
    {
        LOGI("Calibrator::Calibrator: point_index_order[%d] = %d !!!!!!!!!!!!!!", i, point_index_order[i]);
    }
    calibrate_points = target_points;
    reset_calibrate_params();
}

Calibrator::~Calibrator() = default;

void Calibrator::reset_calibrate_params()
{
    calib_left_class.reset_calib_parameter();
    calib_right_class.reset_calib_parameter();

    left_consist_num = 0;  //每个校准点的一致性进度 {1,2,3}
    right_consist_num = 0; //每个校准点的一致性进度 {1,2,3}
    point_index = 0;
    crs_num_threshold = 2;
    finish_calibrating = false;
    left_finish_one_flag = false;
    right_finish_one_flag = false;
    data_isready = false;

    score_left = 0.0;
    score_right = 0.0;

    left_warpMatrix_data.clear();
    right_warpMatrix_data.clear();
    double_left_DetData.clear();
    double_right_DetData.clear();
    double_left_params.clear();
    double_right_params.clear();

    left_clock_pre = Clock::now();//计时
    right_clock_pre = Clock::now();//计时
    time_count_one_point = Clock::now();//计时
}

/*
 * 传入需要校准的点编号，显示点并初始化状态
 */
bool Calibrator::setup_calibrate_point_state(int capture_index)
{
    bool setup_flag = false;
    if (capture_index >= 0 && capture_index < calibration_number)
    {
        setup_flag = true;
        point_index = point_index_order[capture_index];

        // 初始化状态
        left_finish_one_flag = false;
        right_finish_one_flag = false;
        finish_calibrating = false;

        left_consist_num = 0;   //  move to next point
        right_consist_num = 0;  //  move to next point

        // 计时
        left_clock_pre = Clock::now();        //计时
        right_clock_pre = Clock::now();       //计时
        time_count_one_point = Clock::now();  //计时
    }
    return setup_flag;
}

float Calibrator::get_calibrate_score_func() const
{
    float score = (score_left + score_right) / 2.0F;
    return score;
}

calibration_params_data_str Calibrator::get_calibrate_data_and_params_func()
{
    calibration_params_data_str calib_data;
    calib_data.left_data = double_left_DetData;
    calib_data.left_params = double_left_params;
    calib_data.right_data = double_right_DetData;
    calib_data.right_params = double_right_params;
    return calib_data;
}

vector<int> Calibrator::run_calibrating(const frame_detection_result& det_result, bool& calib_next_point, bool& finish_calib, bool& calibrate_succeed, bool* succeed_list)
{
    if(!finish_calibrating)
    {
        calib_next_point = false;
        finish_calib = false;
        chrono::time_point<steady_clock, nanoseconds> click_now; //计时器

        bool det_succeed = det_result.left_eye.valid & det_result.right_eye.valid;

        // --- calculate two eye distance ---
        float eye_dist = -1;
        if (det_succeed)
        {
            eye_dist = det_result.interpupillary_distance;
        }

        // *** calibrate left eye ***
        int left_valid_num = 0;
        vector<bool> left_valid_list;
        vector<Point2f> left_light_points = {det_result.left_eye.light_left, det_result.left_eye.light_right};
        calculate_2points_state(left_light_points, left_valid_list);
        for (auto flag: left_valid_list)
        {
            if(flag){left_valid_num++;}
        }
        LOGI("Calibrator::run_calibrating: left_valid_num = %d!", left_valid_num);
        if (left_valid_num > 0)
        {
            click_now = Clock::now();//计时
            double left_keep_time = (double) std::chrono::duration_cast<std::chrono::nanoseconds>(click_now - left_clock_pre).count() / 1e+6;  // 1 ms = 1e+6 ns
            LOGI("Calibrator::run_calibrating: left_keep_time = %f!", left_keep_time);
            if (left_keep_time > delay_time)
            {
                // delay interval setup time to capture data
                if (!left_finish_one_flag)
                {
                    bool left_data_isready = false;
                    calib_left_class.compare_calib_parameter_consist(det_result.left_eye.pupil, left_light_points, eye_dist, left_finish_one_flag,
                                                                     left_data_isready, left_consist_num, left_valid_num, point_index, crs_num_threshold);
                    if(left_finish_one_flag)
                    {
                        LOGI("Calibrator::run_calibrating: left_finish_one_flag : %d!", point_index);
                        succeed_list[point_index * 2] = true;
                        if(left_data_isready)
                        {
                            // finish capturing data
                            calib_left_class.compute_Matrix_func(left_warpMatrix_data, score_left);
                            LOGI("run_calibrating: compute left eye mapping params succeed!, score is %f", score_left);
                        }
                    }
                    else
                    {
                        LOGI("Calibrator::run_calibrating: left_consist_num = %d!", left_consist_num);
                    }
                }
                left_clock_pre = Clock::now();  // 重新计时
            }
        }

        // *** calibrate right eye ***
        int right_valid_num = 0;
        vector<bool> right_valid_list;
        vector<Point2f> right_light_points = {det_result.right_eye.light_left, det_result.right_eye.light_right};
        calculate_2points_state(right_light_points, right_valid_list);
        for (auto flag: right_valid_list)
        {
            if(flag){right_valid_num++;}
        }
        if (right_valid_num > 0)
        {
            click_now = Clock::now();//计时
            double right_keep_time = (double) std::chrono::duration_cast<std::chrono::nanoseconds>(click_now - right_clock_pre).count() / 1e+6;  // 1 ms = 1e+6 ns
            if (right_keep_time > delay_time)
            {
                // delay interval setup time to capture data
                if (!right_finish_one_flag)
                {
                    bool right_data_isready = false;
                    calib_right_class.compare_calib_parameter_consist(det_result.right_eye.pupil, right_light_points, eye_dist, right_finish_one_flag,
                                                                      right_data_isready, right_consist_num, right_valid_num, point_index, crs_num_threshold);
                    if(right_finish_one_flag)
                    {
                        LOGI("Calibrator::run_calibrating: right_finish_one_flag : %d!", point_index);
                        succeed_list[point_index * 2 + 1] = true;
                        if(right_data_isready)
                        {
                            // finish capturing data
                            calib_right_class.compute_Matrix_func(right_warpMatrix_data, score_right);
                            LOGI("run_calibrating: compute right eye mapping params succeed!, score is %f", score_right);
                        }
                    }
                    else
                    {
                        LOGI("Calibrator::run_calibrating: right_consist_num = %d!", right_consist_num);
                    }
                }
                right_clock_pre = Clock::now();  // 重新计时
            }
        }

        click_now = Clock::now();//计时
        double one_eye_keep_time = (double) std::chrono::duration_cast<std::chrono::nanoseconds>(click_now - time_count_one_point).count() / 1e+6;  // 1 ms = 1e+6 ns
        // delay interval setup crs num
        if (one_eye_keep_time > tolerance_first_time)
        {
            crs_num_threshold = 1;
        }
        else
        {
            crs_num_threshold = 2;
        }
        // give up when time out
        if(one_eye_keep_time > tolerance_second_time)
        {
            LOGI("run_calibrating: one_eye_keep_time is time out!");
            left_finish_one_flag = true;
            right_finish_one_flag = true;
        }

        // finish one point calibration with two eye
        if(left_finish_one_flag && right_finish_one_flag)
        {
            LOGI("Calibrator::run_calibrating left_finish_one_flag && right_finish_one_flag");

            if (point_index == point_index_order[calibration_number-1])
            {
                LOGI("Calibrator::run_calibrating point_index = %d, point_index_order[calibration_number-1] = %d", point_index, point_index_order[calibration_number-1]);
                // finish all points calibration
                // ------ left -------
                bool left_compute_flag = false;
                calib_left_class.get_computed_params_flag(left_compute_flag);
                if(!left_compute_flag)
                {
                    LOGI("run_calibrating: calib_left_class.compute_Matrix_func(left_warpMatrix_data)!");
                    calib_left_class.compute_Matrix_func(left_warpMatrix_data, score_left);
                    calib_left_class.set_finished_capture_flag();
                }

                // ------ right -------
                bool right_compute_flag = false;
                calib_right_class.get_computed_params_flag(right_compute_flag);
                if(!right_compute_flag)
                {
                    LOGI("run_calibrating: calib_left_class.compute_Matrix_func(right_warpMatrix_data)!");
                    calib_right_class.compute_Matrix_func(right_warpMatrix_data, score_right);
                    calib_right_class.set_finished_capture_flag();

                }
                // --- save config result ---
                if(left_warpMatrix_data.size() == 6 && right_warpMatrix_data.size() == 6)
                {
                    double_left_params = convertToDouble(left_warpMatrix_data);
                    double_right_params = convertToDouble(right_warpMatrix_data);

                    calib_left_class.get_calib_original_params_func(double_left_DetData);
                    calib_right_class.get_calib_original_params_func(double_right_DetData);

                    calibrate_succeed = true;
                }
                finish_calibrating = true;
                finish_calib = true;
                calib_next_point = false;
            }
            else
            {
                calib_next_point = true;
                finish_calibrating = false;
            }
        }
    }
    else
    {
        finish_calib = true;
    }
    return {left_consist_num, right_consist_num};
}

