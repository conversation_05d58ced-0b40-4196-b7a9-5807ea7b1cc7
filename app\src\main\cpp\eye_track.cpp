#include "eye_track.h"

eyeTrack::eyeTrack()
{
    // ----- init rknn input buff -----
    data_inputs = (rknn_input*)malloc(sizeof(rknn_input));
    memset(&(data_inputs[0]), 0, sizeof(rknn_input));
    data_inputs[0].index = 0;
    data_inputs[0].pass_through = 0;
    data_inputs[0].type = RKNN_TENSOR_FLOAT32;
    data_inputs[0].fmt = RKNN_TENSOR_NHWC;
    data_inputs[0].size = eye_seg_net_w * eye_seg_net_h * get_type_size(RKNN_TENSOR_FLOAT32);

    // ---- init rknn output buff ----
    outputs = (rknn_output*)malloc(sizeof(rknn_output));
    memset(&(outputs[0]), 0, sizeof(rknn_output));
    outputs[0].want_float = 1;
}
eyeTrack::~eyeTrack()
{
    rknn_destroy(model_ctx);
    free(data_inputs);
    free(outputs);
}

void eyeTrack::load_model(char* model_path, bool& model_isready) {
    int ret = rknn_init(&model_ctx, (void *) model_path, 0, 0, NULL);

    if (ret < 0) {
        printf("eye tracking rknn init fail! ret=%d\n", ret);
        model_isready = false;
    } else {
        printf("eye tracking rknn init succeed!! ret=%d\n", ret);
        model_isready = true;
    }
}

void eyeTrack::inference(const cv::Mat& src, cv::Rect& search_rect, cv::Rect& iris_rect)
{
    iris_rect = Rect(0, 0, 0, 0);
    int pre_bbox_x0 = search_rect.x;
    int pre_bbox_y0 = search_rect.y;

    Mat patch = src(search_rect);

    int img_w = patch.cols;
    int img_h = patch.rows;
    float ratio_w = (float)img_w / (float)eye_seg_net_w;
    float ratio_h = (float)img_h / (float)eye_seg_net_h;
    cv::Mat rs_image;
    cv::resize(patch, rs_image, cv::Size(eye_seg_net_w, eye_seg_net_h));
    // 创建一个目标Mat对象用于存放转换后的数据，类型为CV_32FC1，即单通道float32
    cv::Mat blob_image;
    // 使用convertTo()函数进行类型转换
    rs_image.convertTo(blob_image, CV_32FC1); // 第二个参数是目标类型
    cout << "eye tracking blob_image size = : " << blob_image.size<< endl;

    // --- input set ---
    auto *resize_buf = (unsigned char *)malloc(eye_seg_net_h* eye_seg_net_w * get_type_size(RKNN_TENSOR_FLOAT32));
    size_t dataSize = blob_image.total() * sizeof(float);
    memcpy(resize_buf, blob_image.data, dataSize);
    data_inputs[0].buf = resize_buf;
    int ret = 0;
    ret = rknn_inputs_set(model_ctx, 1, data_inputs);
    check_ret(ret, "eye tracking rknn inputs set");

    // --- deploy ---
    ret = rknn_run(model_ctx, NULL);
    check_ret(ret, "eye tracking rknn run");

    // --- output ---
    ret = rknn_outputs_get(model_ctx, 1, outputs, NULL);
    // 获取buff的指针，用于直接访问内存
    auto* buff_ptr = (float*)outputs[0].buf;
    check_ret(ret, "eye tracking rknn outputs get");

    cv::Mat proto_buffer(eye_seg_net_h, eye_seg_net_w, CV_32F, buff_ptr);

    cv::Mat seg_mask = cv::Mat::zeros(eye_seg_net_h, eye_seg_net_w, CV_8UC1);
    // cv::threshold(proto_buffer, seg_mask, iris_seg_mask_threshold, 255, cv::THRESH_BINARY);
    manual_threahold_float(proto_buffer, seg_mask, iris_seg_mask_threshold, 255);

    /*
    // compute iris rect bbox
    std::vector<cv::Point> points;
    cv::findNonZero(seg_mask, points);
    Rect bbox = boundingRect(points);
    */
    Rect bbox;
    find_boundingRect_from_mask(seg_mask, bbox);

    int bbox_w = (int)((float)bbox.width * ratio_w);
    int bbox_h = (int)((float)bbox.height * ratio_h);
    float rect_ratio = (float)bbox_h / (float)bbox_w;
    if (bbox_w > iris_min_threshold && bbox_h > iris_min_threshold && iris_rect_ratio_min < rect_ratio < iris_rect_ratio_max)
    {
        int bbox_x0 = (int)((float)bbox.x * ratio_w + (float)pre_bbox_x0);
        int bbox_y0 = (int)((float)bbox.y * ratio_h + (float)pre_bbox_y0);
        iris_rect = Rect(bbox_x0, bbox_y0, bbox_w, bbox_h);
    }

    // release input and output buffer
    ret = rknn_outputs_release(model_ctx, 1, outputs);
    check_ret(ret, "eye tracking rknn outputs release");
    delete[] resize_buf;
}