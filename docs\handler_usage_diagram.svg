<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #ffffff; text-shadow: 2px 2px 4px rgba(0,0,0,0.5); }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #ffffff; }
      .text { font-family: Arial, sans-serif; font-size: 13px; fill: #ffffff; font-weight: 500; }
      .small-text { font-family: Arial, sans-serif; font-size: 11px; fill: #f0f0f0; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #ffff00; font-weight: bold; }
      .activity-box { fill: #ff6b35; stroke: #ff4500; stroke-width: 3; filter: drop-shadow(3px 3px 6px rgba(0,0,0,0.3)); }
      .handler-box { fill: #7209b7; stroke: #5a0a8a; stroke-width: 3; filter: drop-shadow(3px 3px 6px rgba(0,0,0,0.3)); }
      .service-box { fill: #00b4d8; stroke: #0077b6; stroke-width: 3; filter: drop-shadow(3px 3px 6px rgba(0,0,0,0.3)); }
      .lifecycle-box { fill: #f72585; stroke: #d00000; stroke-width: 3; filter: drop-shadow(3px 3px 6px rgba(0,0,0,0.3)); }
      .arrow { stroke: #ffffff; stroke-width: 4; fill: none; marker-end: url(#arrowhead); filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.5)); }
      .dashed-arrow { stroke: #ffff00; stroke-width: 3; stroke-dasharray: 8,4; fill: none; marker-end: url(#arrowhead2); }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#ffffff" stroke="#000000" stroke-width="1"/>
    </marker>
    <marker id="arrowhead2" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#ffff00" stroke="#000000" stroke-width="1"/>
    </marker>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background -->
  <rect width="1200" height="800" fill="url(#bgGradient)"/>
  
  <!-- Title Background -->
  <rect x="200" y="10" width="800" height="60" rx="15" fill="#000000" opacity="0.7"/>

  <!-- Title -->
  <text x="600" y="35" text-anchor="middle" class="title">🔥 DetectionActivity1 中 Handler 的使用架构图 🔥</text>
  <text x="600" y="60" text-anchor="middle" class="subtitle">⚡ 眼动追踪检测系统的异步处理机制 ⚡</text>
  
  <!-- DetectionActivity1 -->
  <rect x="50" y="100" width="300" height="200" rx="15" class="activity-box"/>
  <text x="200" y="125" text-anchor="middle" class="subtitle">🎯 DetectionActivity1</text>
  <text x="60" y="150" class="text">📱 主检测Activity</text>
  <text x="60" y="170" class="text">👁️ 管理眼动追踪状态</text>
  <text x="60" y="190" class="text">🌐 处理WebView交互</text>
  <text x="60" y="210" class="text">♻️ 生命周期管理</text>
  <text x="60" y="230" class="code">lifecycleScope.launch { }</text>
  <text x="60" y="250" class="text">⚡ 使用协程而非Handler</text>
  <text x="60" y="270" class="small-text">isGazeTrackingActive: Boolean</text>
  
  <!-- LifecycleHandler Examples -->
  <rect x="450" y="100" width="300" height="200" rx="15" class="handler-box"/>
  <text x="600" y="125" text-anchor="middle" class="subtitle">🔧 其他组件中的Handler使用</text>
  <text x="460" y="150" class="text">📝 DetectionCodeDetectionFragment:</text>
  <text x="460" y="170" class="code">mHandler.postDelayed({...}, 800)</text>
  <text x="460" y="190" class="text">📷 ScanCodeDetectionFragment:</text>
  <text x="460" y="210" class="code">mHandler.removeCallbacks(...)</text>
  <text x="460" y="230" class="text">🔄 GazeTrackService:</text>
  <text x="460" y="250" class="code">handleMessage(msg: Message)</text>
  <text x="460" y="270" class="text">⚙️ CalibrationActivity:</text>
  <text x="460" y="290" class="code">parseMessage(msg)</text>
  
  <!-- GazeTrackingManager -->
  <rect x="850" y="100" width="300" height="200" rx="15" class="service-box"/>
  <text x="1000" y="125" text-anchor="middle" class="subtitle">🚀 GazeTrackingManager</text>
  <text x="860" y="150" class="text">👁️‍🗨️ 眼动追踪管理器</text>
  <text x="860" y="170" class="text">▶️ startGazeTracking()</text>
  <text x="860" y="190" class="text">⏹️ stopGazeTracking()</text>
  <text x="860" y="210" class="text">📡 与Service通信</text>
  <text x="860" y="230" class="code">Messenger + Handler</text>
  <text x="860" y="250" class="text">🔄 进程间通信</text>
  <text x="860" y="270" class="small-text">🏃‍♂️ 独立进程运行</text>
  
  <!-- Lifecycle Management -->
  <rect x="50" y="350" width="500" height="150" rx="15" class="lifecycle-box"/>
  <text x="300" y="375" text-anchor="middle" class="subtitle">♻️ 生命周期管理 (DetectionActivity1)</text>
  <text x="60" y="400" class="text">⏸️ onPause(): 暂停眼动追踪以释放相机资源</text>
  <text x="60" y="420" class="text">▶️ onResume(): 恢复眼动追踪 (延迟500ms)</text>
  <text x="60" y="440" class="text">💀 onDestroy(): 清理眼动追踪资源</text>
  <text x="60" y="460" class="code">lifecycleScope.launch { kotlinx.coroutines.delay(500) }</text>
  <text x="60" y="480" class="small-text">⚡ 使用协程替代Handler进行延时操作</text>
  
  <!-- Handler vs Coroutines Comparison -->
  <rect x="600" y="350" width="550" height="150" rx="15" class="handler-box"/>
  <text x="875" y="375" text-anchor="middle" class="subtitle">⚔️ Handler vs 协程对比</text>
  <text x="610" y="400" class="text">🔧 传统Handler方式:</text>
  <text x="610" y="420" class="code">handler.postDelayed({ doSomething() }, 500)</text>
  <text x="610" y="440" class="text">🚀 现代协程方式 (DetectionActivity1使用):</text>
  <text x="610" y="460" class="code">lifecycleScope.launch { delay(500); doSomething() }</text>
  <text x="610" y="480" class="small-text">✨ 更好的生命周期管理，自动取消，避免内存泄漏</text>
  
  <!-- WebView Interaction -->
  <rect x="50" y="550" width="350" height="120" rx="15" class="activity-box"/>
  <text x="225" y="575" text-anchor="middle" class="subtitle">🌐 WebView JavaScript 交互</text>
  <text x="60" y="600" class="text">▶️ onStartGazeTracking()</text>
  <text x="60" y="620" class="text">⏹️ onStopGazeTracking()</text>
  <text x="60" y="640" class="code">wbHrv.evaluateJavascript("...", null)</text>
  <text x="60" y="660" class="small-text">📞 通过JavaScript回调通知Web页面</text>

  <!-- Message Flow -->
  <rect x="450" y="550" width="350" height="120" rx="15" class="service-box"/>
  <text x="625" y="575" text-anchor="middle" class="subtitle">📡 消息流转机制</text>
  <text x="460" y="600" class="text">1️⃣ Activity → GazeTrackingManager</text>
  <text x="460" y="620" class="text">2️⃣ Manager → GazeTrackService</text>
  <text x="460" y="640" class="text">3️⃣ Service → Handler.parseMessage()</text>
  <text x="460" y="660" class="small-text">📨 使用Messenger进行进程间通信</text>

  <!-- Key Features -->
  <rect x="850" y="550" width="300" height="120" rx="15" class="lifecycle-box"/>
  <text x="1000" y="575" text-anchor="middle" class="subtitle">🎯 关键特性</text>
  <text x="860" y="600" class="text">✅ 协程替代Handler延时</text>
  <text x="860" y="620" class="text">✅ 自动生命周期管理</text>
  <text x="860" y="640" class="text">✅ 内存泄漏防护</text>
  <text x="860" y="660" class="text">✅ 异常处理机制</text>
  
  <!-- Arrows -->
  <line x1="350" y1="200" x2="450" y2="200" class="arrow"/>
  <line x1="750" y1="200" x2="850" y2="200" class="arrow"/>
  <line x1="200" y1="300" x2="200" y2="350" class="arrow"/>
  <line x1="225" y1="500" x2="225" y2="550" class="arrow"/>
  <line x1="400" y1="200" x2="625" y2="550" class="dashed-arrow"/>
  
  <!-- Legend -->
  <rect x="50" y="720" width="1100" height="60" rx="15" fill="#000000" opacity="0.8" stroke="#ffffff" stroke-width="2"/>
  <text x="600" y="740" text-anchor="middle" class="subtitle">📋 说明</text>
  <text x="60" y="760" class="text">🚀 DetectionActivity1 采用现代协程方式处理异步操作，而其他组件仍使用传统Handler机制</text>
  <text x="60" y="775" class="small-text">🧡橙色=Activity组件, 💜紫色=Handler使用, 💙蓝色=服务组件, 💗粉色=生命周期管理</text>
</svg>
