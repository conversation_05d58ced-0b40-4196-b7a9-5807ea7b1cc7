<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .label { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .code-text { font-family: 'Courier New', monospace; font-size: 11px; text-anchor: start; }
      .application { fill: #e6f3ff; stroke: #0066cc; stroke-width: 3; }
      .object-singleton { fill: #ffe6cc; stroke: #ff6600; stroke-width: 2; }
      .static-manager { fill: #e6ffe6; stroke: #00cc00; stroke-width: 2; }
      .traditional-singleton { fill: #ffe6f3; stroke: #cc0066; stroke-width: 2; }
      .activity { fill: #f0f0f0; stroke: #666; stroke-width: 2; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .init-arrow { stroke: #0066cc; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .access-arrow { stroke: #ff6600; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" class="title">LiveData + observe vs LiveEventBus 架构对比</text>
  
  <!-- LiveData方式 -->
  <text x="300" y="70" class="subtitle">LiveData + observe 方式</text>
  
  <!-- LoginActivity -->
  <rect x="50" y="100" width="120" height="60" class="activity" rx="5"/>
  <text x="110" y="125" class="label">LoginActivity</text>
  <text x="110" y="140" class="small-text">用户登录</text>
  
  <!-- LoginViewModel -->
  <rect x="50" y="200" width="120" height="60" class="viewmodel" rx="5"/>
  <text x="110" y="225" class="label">LoginViewModel</text>
  <text x="110" y="240" class="small-text">处理登录逻辑</text>
  
  <!-- GlobalUserRepository -->
  <rect x="250" y="300" width="140" height="60" class="repository" rx="5"/>
  <text x="320" y="325" class="label">GlobalUserRepository</text>
  <text x="320" y="340" class="small-text">全局用户状态</text>
  
  <!-- MainViewModel -->
  <rect x="450" y="200" width="120" height="60" class="viewmodel" rx="5"/>
  <text x="510" y="225" class="label">MainViewModel</text>
  <text x="510" y="240" class="small-text">主页逻辑</text>
  
  <!-- ProfileViewModel -->
  <rect x="450" y="400" width="120" height="60" class="viewmodel" rx="5"/>
  <text x="510" y="425" class="label">ProfileViewModel</text>
  <text x="510" y="440" class="small-text">个人页逻辑</text>
  
  <!-- MainActivity -->
  <rect x="450" y="100" width="120" height="60" class="activity" rx="5"/>
  <text x="510" y="125" class="label">MainActivity</text>
  <text x="510" y="140" class="small-text">主页面</text>
  
  <!-- ProfileActivity -->
  <rect x="450" y="500" width="120" height="60" class="activity" rx="5"/>
  <text x="510" y="525" class="label">ProfileActivity</text>
  <text x="510" y="540" class="small-text">个人页面</text>
  
  <!-- 依赖注入容器 -->
  <rect x="250" y="100" width="140" height="60" class="dependency" rx="5"/>
  <text x="320" y="125" class="label">依赖注入容器</text>
  <text x="320" y="140" class="small-text">Dagger/Hilt</text>
  
  <!-- LiveData方式的箭头 -->
  <line x1="110" y1="160" x2="110" y2="200" class="arrow"/>
  <line x1="110" y1="260" x2="250" y2="330" class="arrow"/>
  <line x1="390" y1="330" x2="450" y2="230" class="arrow"/>
  <line x1="390" y1="330" x2="450" y2="430" class="arrow"/>
  <line x1="510" y1="200" x2="510" y2="160" class="arrow"/>
  <line x1="510" y1="460" x2="510" y2="500" class="arrow"/>
  
  <!-- 依赖注入的虚线箭头 -->
  <line x1="320" y1="160" x2="110" y2="200" class="dotted-arrow"/>
  <line x1="320" y1="160" x2="320" y2="300" class="dotted-arrow"/>
  <line x1="390" y1="130" x2="450" y2="230" class="dotted-arrow"/>
  <line x1="390" y1="130" x2="450" y2="430" class="dotted-arrow"/>
  
  <!-- LiveEventBus方式 -->
  <text x="900" y="70" class="subtitle">LiveEventBus 方式</text>
  
  <!-- LiveEventBus中心 -->
  <circle cx="900" cy="300" r="80" class="eventbus"/>
  <text x="900" y="295" class="label">LiveEventBus</text>
  <text x="900" y="310" class="small-text">全局事件总线</text>
  
  <!-- Activities around EventBus -->
  <rect x="750" y="100" width="120" height="60" class="activity" rx="5"/>
  <text x="810" y="125" class="label">LoginActivity</text>
  <text x="810" y="140" class="small-text">发送登录事件</text>
  
  <rect x="1030" y="100" width="120" height="60" class="activity" rx="5"/>
  <text x="1090" y="125" class="label">MainActivity</text>
  <text x="1090" y="140" class="small-text">监听登录事件</text>
  
  <rect x="1030" y="300" width="120" height="60" class="activity" rx="5"/>
  <text x="1090" y="325" class="label">ProfileActivity</text>
  <text x="1090" y="340" class="small-text">监听登录事件</text>
  
  <rect x="1030" y="500" width="120" height="60" class="activity" rx="5"/>
  <text x="1090" y="525" class="label">DetectionActivity</text>
  <text x="1090" y="540" class="small-text">监听登录事件</text>
  
  <rect x="750" y="500" width="120" height="60" class="activity" rx="5"/>
  <text x="810" y="525" class="label">SettingsActivity</text>
  <text x="810" y="540" class="small-text">监听登录事件</text>
  
  <!-- LiveEventBus的箭头 -->
  <line x1="830" y1="160" x2="860" y2="240" class="arrow"/>
  <line x1="940" y1="240" x2="1030" y2="130" class="arrow"/>
  <line x1="980" y1="300" x2="1030" y2="330" class="arrow"/>
  <line x1="940" y1="360" x2="1030" y2="530" class="arrow"/>
  <line x1="860" y1="360" x2="830" y2="500" class="arrow"/>
  
  <!-- 优缺点对比 -->
  <text x="300" y="620" class="subtitle">复杂度高，需要依赖注入</text>
  <text x="300" y="640" class="small-text">• 需要ViewModel层</text>
  <text x="300" y="655" class="small-text">• 需要Repository层</text>
  <text x="300" y="670" class="small-text">• 需要依赖注入框架</text>
  <text x="300" y="685" class="small-text">• 组件间耦合度高</text>
  
  <text x="900" y="620" class="subtitle">简单直接，完全解耦</text>
  <text x="900" y="640" class="small-text">• 直接发送/接收事件</text>
  <text x="900" y="655" class="small-text">• 无需中间层</text>
  <text x="900" y="670" class="small-text">• 组件完全独立</text>
  <text x="900" y="685" class="small-text">• 生命周期安全</text>
  
  <!-- 分割线 -->
  <line x1="600" y1="80" x2="600" y2="580" stroke="#ccc" stroke-width="2"/>
</svg>
