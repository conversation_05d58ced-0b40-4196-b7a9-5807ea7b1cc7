<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .code { font-family: 'Courier New', monospace; font-size: 10px; fill: #e74c3c; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .data-flow { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; }
      .ui-update { fill: #fff3cd; stroke: #f39c12; stroke-width: 2; }
      .view-ops { fill: #e3f2fd; stroke: #3498db; stroke-width: 2; }
      .native-ops { fill: #f3e5f5; stroke: #9b59b6; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #27ae60; stroke-width: 3; fill: none; marker-end: url(#greenarrow); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
    <marker id="greenarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">🔧 updateUI 具体实现机制详解</text>
  
  <!-- 数据流向图 -->
  <rect x="50" y="60" width="1500" height="120" rx="15" class="data-flow"/>
  <text x="800" y="85" text-anchor="middle" class="subtitle">📊 数据流向: Repository → ViewModel → LiveData → UI</text>
  
  <!-- 数据流箭头 -->
  <line x1="150" y1="120" x2="250" y2="120" class="flow-arrow"/>
  <line x1="350" y1="120" x2="450" y2="120" class="flow-arrow"/>
  <line x1="550" y1="120" x2="650" y2="120" class="flow-arrow"/>
  <line x1="750" y1="120" x2="850" y2="120" class="flow-arrow"/>
  <line x1="950" y1="120" x2="1050" y2="120" class="flow-arrow"/>
  <line x1="1150" y1="120" x2="1250" y2="120" class="flow-arrow"/>
  <line x1="1350" y1="120" x2="1450" y2="120" class="flow-arrow"/>
  
  <text x="100" y="115" class="text">🌐 网络请求</text>
  <text x="300" y="115" class="text">📦 Repository</text>
  <text x="500" y="115" class="text">🧠 ViewModel</text>
  <text x="700" y="115" class="text">📡 LiveData</text>
  <text x="900" y="115" class="text">👀 Observer</text>
  <text x="1100" y="115" class="text">🔧 updateUI()</text>
  <text x="1300" y="115" class="text">🎨 View更新</text>
  
  <text x="100" y="135" class="small-text">API调用</text>
  <text x="300" y="135" class="small-text">数据处理</text>
  <text x="500" y="135" class="small-text">业务逻辑</text>
  <text x="700" y="135" class="small-text">postValue</text>
  <text x="900" y="135" class="small-text">observe回调</text>
  <text x="1100" y="135" class="small-text">具体实现</text>
  <text x="1300" y="135" class="small-text">UI渲染</text>

  <!-- updateUserInfo 具体实现 -->
  <rect x="50" y="200" width="750" height="280" rx="15" class="ui-update"/>
  <text x="425" y="225" text-anchor="middle" class="subtitle">👤 updateUserInfo() 实现详解</text>
  
  <text x="60" y="250" class="text">🔍 <tspan class="subtitle">方法签名:</tspan></text>
  <text x="60" y="270" class="code">private fun updateUserInfo(user: User?) {</text>
  
  <text x="60" y="295" class="text">📊 <tspan class="subtitle">1. 数据处理:</tspan></text>
  <text x="60" y="315" class="code">val userGender = UserManager.getUserGender(user)</text>
  
  <text x="60" y="340" class="text">🖼️ <tspan class="subtitle">2. 头像更新:</tspan></text>
  <text x="60" y="360" class="code">when(userGender) {</text>
  <text x="60" y="380" class="code">  Gender.FEMALE -> ImageLoader.loadImageWithPlaceholder(</text>
  <text x="60" y="400" class="code">    this, user?.avatar, R.drawable.ic_female_avatar_round, ivUserAvatar)</text>
  <text x="60" y="420" class="code">  else -> ImageLoader.loadImageWithPlaceholder(...)</text>
  <text x="60" y="440" class="code">}</text>
  
  <text x="60" y="465" class="text">📝 <tspan class="subtitle">3. 文本信息更新:</tspan></text>
  <text x="60" y="485" class="code">tvUserName.isVisible = user?.username.orEmpty().isNotBlank()</text>
  <text x="60" y="505" class="code">tvUserName.text = user?.username</text>
  <text x="60" y="525" class="code">tvUserPhone.text = getString(R.string.str_phone_last_number_, user?.phone)</text>

  <!-- updateDeviceInfo 具体实现 -->
  <rect x="820" y="200" width="730" height="280" rx="15" class="ui-update"/>
  <text x="1185" y="225" text-anchor="middle" class="subtitle">🔧 updateDeviceInfo() 实现详解</text>
  
  <text x="830" y="250" class="text">🔍 <tspan class="subtitle">方法签名:</tspan></text>
  <text x="830" y="270" class="code">private fun updateDeviceInfo(deviceInfo: DeviceInfo?) {</text>
  
  <text x="830" y="295" class="text">🖼️ <tspan class="subtitle">1. Logo更新:</tspan></text>
  <text x="830" y="315" class="code">ImageLoader.loadImageWithPlaceholder(this,</text>
  <text x="830" y="335" class="code">  deviceInfo?.logo, 0, R.drawable.ic_main_logo, binding.ivLogo)</text>
  
  <text x="830" y="360" class="text">📝 <tspan class="subtitle">2. 版权信息:</tspan></text>
  <text x="830" y="380" class="code">binding.tvCopyright.text = deviceInfo?.copyright</text>
  
  <text x="830" y="405" class="text">🎯 <tspan class="subtitle">3. 界面切换:</tspan></text>
  <text x="830" y="425" class="code">when(DeviceManager.getStartupMode(deviceInfo)) {</text>
  <text x="830" y="445" class="code">  QRCODE_WECHAT -> showScanCode()</text>
  <text x="830" y="465" class="code">  ACCESS_CODE -> showDetectionCode()</text>
  <text x="830" y="485" class="code">  else -> showException()</text>
  <text x="830" y="505" class="code">}</text>

  <!-- View操作方法详解 -->
  <rect x="50" y="500" width="1500" height="200" rx="15" class="view-ops"/>
  <text x="800" y="525" text-anchor="middle" class="subtitle">🎨 View 操作方法详解</text>
  
  <!-- 文本操作 -->
  <text x="60" y="550" class="text">📝 <tspan class="subtitle">文本操作:</tspan></text>
  <text x="60" y="570" class="code">textView.text = "新文本"                    // 直接设置文本</text>
  <text x="60" y="590" class="code">textView.setText(R.string.str_title)        // 使用资源ID</text>
  <text x="60" y="610" class="code">textView.isVisible = user != null           // 控制可见性</text>
  
  <!-- 图片操作 -->
  <text x="400" y="550" class="text">🖼️ <tspan class="subtitle">图片操作:</tspan></text>
  <text x="400" y="570" class="code">imageView.setImageResource(R.drawable.icon)  // 设置资源图片</text>
  <text x="400" y="590" class="code">imageView.setImageBitmap(bitmap)             // 设置位图</text>
  <text x="400" y="610" class="code">ImageLoader.loadImageWithPlaceholder(...)    // 异步加载</text>
  
  <!-- 背景和样式 -->
  <text x="800" y="550" class="text">🎨 <tspan class="subtitle">背景样式:</tspan></text>
  <text x="800" y="570" class="code">view.setBackgroundResource(R.drawable.bg)    // 设置背景</text>
  <text x="800" y="590" class="code">view.setBackgroundColor(Color.RED)           // 设置颜色</text>
  <text x="800" y="610" class="code">view.background = null                       // 清除背景</text>
  
  <!-- 布局和动画 -->
  <text x="1200" y="550" class="text">📐 <tspan class="subtitle">布局动画:</tspan></text>
  <text x="1200" y="570" class="code">progressBar.progress = 50                    // 进度更新</text>
  <text x="1200" y="590" class="code">ObjectAnimator.ofFloat(view, "rotation", 360f) // 动画</text>
  <text x="1200" y="610" class="code">windowManager.updateViewLayout(view, params) // 布局更新</text>
  
  <text x="60" y="630" class="small-text">💡 所有这些操作都必须在主线程(UI线程)中执行</text>
  <text x="60" y="650" class="small-text">⚠️ 在子线程中需要使用 runOnUiThread 或 Handler.post 切换到主线程</text>
  <text x="60" y="670" class="small-text">🔧 ViewBinding 提供类型安全的 View 访问: binding.textView.text = "内容"</text>

  <!-- 特殊UI更新场景 -->
  <rect x="50" y="720" width="750" height="200" rx="15" class="native-ops"/>
  <text x="425" y="745" text-anchor="middle" class="subtitle">🚀 特殊UI更新场景</text>
  
  <text x="60" y="770" class="text">🎯 <tspan class="subtitle">悬浮窗更新 (DotView):</tspan></text>
  <text x="60" y="790" class="code">fun update(x: Int, y: Int) {</text>
  <text x="60" y="810" class="code">  dotParams.x = x; dotParams.y = y</text>
  <text x="60" y="830" class="code">  windowManager.updateViewLayout(this, dotParams)</text>
  <text x="60" y="850" class="code">}</text>
  
  <text x="60" y="875" class="text">📱 <tspan class="subtitle">Fragment切换:</tspan></text>
  <text x="60" y="895" class="code">supportFragmentManager.beginTransaction()</text>
  <text x="60" y="915" class="code">  .replace(R.id.container, fragment).commit()</text>

  <!-- Native层UI更新 -->
  <rect x="820" y="720" width="730" height="200" rx="15" class="native-ops"/>
  <text x="1185" y="745" text-anchor="middle" class="subtitle">🔧 Native层UI更新</text>
  
  <text x="830" y="770" class="text">🎨 <tspan class="subtitle">OpenCV图像绘制:</tspan></text>
  <text x="830" y="790" class="code">void GazeService::draw(const Mat& img) {</text>
  <text x="830" y="810" class="code">  ANativeWindow_setBuffersGeometry(window, img.cols, img.rows)</text>
  <text x="830" y="830" class="code">  // 逐行拷贝像素数据到buffer</text>
  <text x="830" y="850" class="code">  memcpy(dstData + i * dstlineSize, srcData + i * srclineSize)</text>
  <text x="830" y="870" class="code">}</text>
  
  <text x="830" y="895" class="text">👁️ <tspan class="subtitle">眼动追踪视点更新:</tspan></text>
  <text x="830" y="915" class="code">pq_blr->SetPos(screen_x, screen_y)->Done()</text>

  <!-- 最佳实践 -->
  <rect x="50" y="940" width="1500" height="220" rx="15" class="data-flow"/>
  <text x="800" y="965" text-anchor="middle" class="subtitle">💡 updateUI 最佳实践</text>
  
  <text x="60" y="990" class="text">🎯 <tspan class="subtitle">1. 线程安全:</tspan></text>
  <text x="60" y="1010" class="small-text">   • 所有UI操作必须在主线程执行</text>
  <text x="60" y="1030" class="small-text">   • 使用 runOnUiThread、Handler.post 或协程的 Dispatchers.Main 切换线程</text>
  
  <text x="400" y="990" class="text">🔄 <tspan class="subtitle">2. 生命周期管理:</tspan></text>
  <text x="400" y="1010" class="small-text">   • 使用 LiveData.observe() 自动管理生命周期</text>
  <text x="400" y="1030" class="small-text">   • 避免在 onDestroy 后更新UI，防止内存泄漏</text>
  
  <text x="800" y="990" class="text">⚡ <tspan class="subtitle">3. 性能优化:</tspan></text>
  <text x="800" y="1010" class="small-text">   • 避免频繁的UI更新，使用防抖动机制</text>
  <text x="800" y="1030" class="small-text">   • 大量数据使用 RecyclerView 而不是直接更新多个View</text>
  
  <text x="1200" y="990" class="text">🛡️ <tspan class="subtitle">4. 错误处理:</tspan></text>
  <text x="1200" y="1010" class="small-text">   • 检查 View 是否为 null</text>
  <text x="1200" y="1030" class="small-text">   • 使用 try-catch 包装可能失败的UI操作</text>
  
  <text x="60" y="1055" class="text">📊 <tspan class="subtitle">5. 数据绑定:</tspan></text>
  <text x="60" y="1075" class="small-text">   • 优先使用 ViewBinding 获得类型安全</text>
  <text x="60" y="1095" class="small-text">   • 考虑使用 DataBinding 实现双向绑定</text>
  
  <text x="400" y="1055" class="text">🔧 <tspan class="subtitle">6. 状态管理:</tspan></text>
  <text x="400" y="1075" class="small-text">   • 使用 isVisible 而不是 View.GONE/VISIBLE</text>
  <text x="400" y="1095" class="small-text">   • 保存和恢复UI状态，处理配置变更</text>
  
  <text x="800" y="1055" class="text">🎨 <tspan class="subtitle">7. 用户体验:</tspan></text>
  <text x="800" y="1075" class="small-text">   • 提供加载状态和错误提示</text>
  <text x="800" y="1095" class="small-text">   • 使用动画让UI变化更自然</text>
  
  <text x="1200" y="1055" class="text">🧪 <tspan class="subtitle">8. 测试友好:</tspan></text>
  <text x="1200" y="1075" class="small-text">   • 将UI逻辑从业务逻辑中分离</text>
  <text x="1200" y="1095" class="small-text">   • 使用 Espresso 进行UI测试</text>
  
  <text x="60" y="1130" class="text">🔄 <tspan class="subtitle">典型更新流程:</tspan> 网络请求 → Repository处理 → ViewModel发布 → LiveData通知 → Observer回调 → updateUI()执行 → View属性更新 → 用户看到变化</text>
  <text x="60" y="1150" class="small-text">💼 当前项目特点: 结合传统Handler和现代MVVM，在不同场景下灵活选择最适合的UI更新方案</text>

</svg>
