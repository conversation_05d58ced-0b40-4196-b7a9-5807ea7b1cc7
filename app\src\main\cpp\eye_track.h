#ifndef EYE_TRACK_H
#define EYE_TRACK_H


#include "utils.h"


#define eye_seg_net_w 160
#define eye_seg_net_h 128

#define pupil_min_threshold 10
#define iris_min_threshold 20

#define iris_rect_ratio_min 0.5
#define iris_rect_ratio_max 2.0
#define iris_seg_mask_threshold 0.6

#define update_iris_rect_rate 0.5

class eyeTrack
{
public:
    eyeTrack();
    ~eyeTrack();
    void load_model(char* model_path, bool& model_isready);
    void inference(const cv::Mat& src, cv::Rect& search_rect, cv::Rect& iris_rect);

private:
    rknn_context model_ctx;
    rknn_input *data_inputs;
    rknn_output *outputs;
};

#endif //EYE_TRACK_H