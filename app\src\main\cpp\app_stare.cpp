//
// Created by knight on 2024/12/29.
//

#include "app_stare.h"

GazeStare::GazeStare()
{
    target_point = Point2f(-1.0F, -1.0F);
    gaze_data_list.clear();
}

GazeStare::~GazeStare(){}

void GazeStare::reset_params_func()
{
    gaze_data_list.clear();
}

// 设置注视的目标点位置
bool GazeStare::set_target_point(float x, float y)
{
    bool flag = false;
    if(x > 0 && x < 1.0 && y > 0 && y < 1.0)
    {
        target_point = Point2f(x, y);
        flag = true;
    }
    return flag;
}

// 应用期间传输数据
void GazeStare::collect_data(float x, float y, float duration)
{
    gaze_data_list.emplace_back(Point3f(x, y, duration));
}

// 处理眼动轨迹的数据
std::vector<std::vector<Point3f>> GazeStare::postprocess_trajectory_data()
{
    std::vector<std::vector<Point3f>> stare_points;
    if(!gaze_data_list.empty())
    {
        int num_p = (int)gaze_data_list.size();
        Point3f pre_p = gaze_data_list[0];
        vector<Point3f> line_poins = {pre_p};
        for (int i = 1; i < num_p; i++)
        {
            Point3f p1 = gaze_data_list[i - 1];
            Point3f p2 = gaze_data_list[i];
            float grad1 = abs(p2.x - p1.x) + abs(p2.y - p1.y);
            float grad2 = abs(p2.x - pre_p.x) + abs(p2.y - pre_p.y);
            if(grad1 < app_filter_thres)
            {
                if (grad2 < app_merge_thres)
                {
                    line_poins.push_back(p2);
                }
                else
                {
                    pre_p = p2;
                    stare_points.push_back(line_poins);
                    line_poins.clear();
                    line_poins.push_back(pre_p);
                }
            }
        }
    }
    return stare_points;
}

// 获取json的数据
string GazeStare::get_record_jsondata()
{
    string json_data = "{'gaze': []}";
    std::vector<std::vector<Point3f>> stare_points = postprocess_trajectory_data();
    if(!stare_points.empty())
    {
        json_data = transfer_trajectory_to_jsondata(stare_points);
    }
    return json_data;
}