//
// Created by user on 2024/12/12.
//

#include "detection.h"

Detection::Detection(const char * config_dir){
    // --- load assert file ---
    string face_det_model_file = config_dir + face_det_model_name;
    string eye_tracking_model_file = config_dir +  eye_tracking_model_name;
    string lights_det_model_file = config_dir +  lights_det_model_name;
    string pupil_seg_model_file = config_dir +  pupil_seg_model_name;

    // --- 模型加载 --
    // ** 加载人脸检测模型 **
    face_det_model_isready = false;
    char *mutable_face_det_model_file = new char[face_det_model_file.length() + 1]; // 注意+1来存放字符串结束符'\0'
    strcpy(mutable_face_det_model_file, face_det_model_file.c_str()); // 复制字符串
    face_det.load_model(mutable_face_det_model_file, face_det_model_isready);
    delete[] mutable_face_det_model_file;

    // ** 加载眼睛跟踪模型 **
    eye_track_model_isready = false;
    char *mutable_eye_tracking_model_file = new char[eye_tracking_model_file.length() + 1]; // 注意+1来存放字符串结束符'\0'
    strcpy(mutable_eye_tracking_model_file, eye_tracking_model_file.c_str()); // 复制字符串
    eye_track_seg.load_model(mutable_eye_tracking_model_file, eye_track_model_isready);
    delete[] mutable_eye_tracking_model_file;

    // ** 加载关键点检测模型 **
    lights_det_model_isready = false;
    char *mutable_lights_det_model_file = new char[lights_det_model_file.length() + 1]; // 注意+1来存放字符串结束符'\0'
    char *mutable_pupil_seg_model_file = new char[pupil_seg_model_file.length() + 1]; // 注意+1来存放字符串结束符'\0'
    strcpy(mutable_lights_det_model_file, lights_det_model_file.c_str()); // 复制字符串
    strcpy(mutable_pupil_seg_model_file, pupil_seg_model_file.c_str()); // 复制字符串
    lights_det.load_model(mutable_lights_det_model_file, mutable_pupil_seg_model_file,
                          lights_det_model_isready);
    delete[] mutable_lights_det_model_file;
    delete[] mutable_pupil_seg_model_file;

    // init result
    left_eye_rect = cv::Rect(0, 0, 0, 0);
    left_iris_rect = cv::Rect(0, 0, 0, 0);
    left_lights_point_list.assign(2, cv::Point2f(-1.0F, -1.0F));
    left_pupil_cent_point = cv::Point2f(-1.0F, -1.0F);

    right_eye_rect = cv::Rect(0, 0, 0, 0);
    right_iris_rect = cv::Rect(0, 0, 0, 0);
    right_lights_point_list.assign(2, cv::Point2f(-1.0F, -1.0F));
    right_pupil_cent_point = cv::Point2f(-1.0F, -1.0F);

    record_interpupillary_distance = -1.0F;
    pose_distance = 0;

    det_failed = true;
}

Detection::~Detection() = default;

void Detection::reset_detection_parameters()
{
    // init result
    left_iris_rect = cv::Rect(0, 0, 0, 0);
    left_lights_point_list.assign(2, cv::Point2f(-1.0F, -1.0F));
    left_pupil_cent_point = cv::Point2f(-1.0F, -1.0F);

    right_iris_rect = cv::Rect(0, 0, 0, 0);
    right_lights_point_list.assign(2, cv::Point2f(-1.0F, -1.0F));
    right_pupil_cent_point = cv::Point2f(-1.0F, -1.0F);
}

one_eye_detect_result Detection::assignment_func(std::vector<cv::Point2f> & lights_point, cv::Point2f & pupil_point, cv::Rect& iris)
{
    one_eye_detect_result tmp;
    tmp.valid = true;
    tmp.pupil = pupil_point;
    tmp.light_left = lights_point[0];
    tmp.light_right = lights_point[1];
    tmp.iris_rect = iris;

    return tmp;
}

/**
 * 特征检测过程
 */
frame_detection_result Detection::detection(const cv::Mat &Image) {
    // --- reset detection parameter ---
    reset_detection_parameters();
    frame_detection_result det_result;

    // 单通道图像
    cv::Mat redChannel = Image.clone();
    LOGI("------- Detection::detection: redChannel.shape = (%d, %d)!!!! ------", redChannel.rows, redChannel.cols);
    if (det_failed) {
        // 全图范围检测两只眼睛的区域
        LOGI("------- Detection::detection : face_det.recognize!!!------");
        face_det.recognize(redChannel, left_eye_rect, right_eye_rect, det_failed);
    }
    // 眼睛跟踪与特征提取
    // --------- processing left eye ---------
    bool det_failed_left = true;
    if (left_eye_rect.width > 0 && left_eye_rect.height > 0) {
        eye_track_seg.inference(redChannel, left_eye_rect, left_iris_rect);
        if (left_iris_rect.width > 0 && left_iris_rect.height > 0) {
            create_eye_tracking_rect_func(left_iris_rect, left_eye_rect, redChannel.cols,
                                          redChannel.rows);
            lights_det.recognize(redChannel, left_iris_rect, left_lights_point_list,
                                 left_pupil_cent_point);
            if (left_pupil_cent_point.x > 0 && left_pupil_cent_point.y > 0) {
                det_failed_left = false;
                det_result.left_eye = assignment_func(left_lights_point_list, left_pupil_cent_point, left_iris_rect);

            }
            else
            {
                left_eye_rect = Rect(0,0,0,0);
            }
        }
    }

    // --------- processing right eye ---------
    bool det_failed_right = true;
    if (right_eye_rect.width > 0 && right_eye_rect.height > 0) {
        eye_track_seg.inference(redChannel, right_eye_rect, right_iris_rect);
        if (right_iris_rect.width > 0 && right_iris_rect.height > 0) {
            create_eye_tracking_rect_func(right_iris_rect, right_eye_rect, redChannel.cols,
                                          redChannel.rows);
            lights_det.recognize(redChannel, right_iris_rect, right_lights_point_list,
                                 right_pupil_cent_point);
            if (right_pupil_cent_point.x > 0 && right_pupil_cent_point.y > 0) {
                det_failed_right = false;
                det_result.right_eye = assignment_func(right_lights_point_list, right_pupil_cent_point, right_iris_rect);
            }
            else
            {
                right_eye_rect = Rect(0,0,0,0);
            }
        }
    }

    // 计算有效瞳距
    if(!det_failed_left && !det_failed_right)
    {
        record_interpupillary_distance = sqrt(powf(left_pupil_cent_point.x - right_pupil_cent_point.x, 2) +
                                              powf(left_pupil_cent_point.y - right_pupil_cent_point.y, 2));
        LOGI("------- Detection::detection : record_interpupillary_distance = %.3f!!!------", record_interpupillary_distance);
        det_result.interpupillary_distance = record_interpupillary_distance;
    }

    if (det_failed_left || det_failed_right) {
        det_failed = true;
    } else {
        // 判断检测框的比例
        double two_eye_iris_ratio = (double) left_iris_rect.width / (double) right_iris_rect.width;
        float left_iris_distance_ratio = (float) left_iris_rect.width / record_interpupillary_distance;
        float right_iris_distance_ratio = (float) right_iris_rect.width / record_interpupillary_distance;

        if (two_eye_iris_ratio < two_eye_det_rect_ratio_min ||
            two_eye_iris_ratio > two_eye_det_rect_ratio_max ||
            left_iris_distance_ratio > iris_width_eye_distance_ratio_max ||
            left_iris_distance_ratio < iris_width_eye_distance_ratio_min ||
            right_iris_distance_ratio > iris_width_eye_distance_ratio_max ||
            right_iris_distance_ratio < iris_width_eye_distance_ratio_min) {
            left_eye_rect = Rect(0,0,0,0);
            right_eye_rect = Rect(0,0,0,0);

            det_failed = true;

            LOGI("------- Detection::detection : det_failed, two_eye_iris_ratio = %.3f!!!------", two_eye_iris_ratio);
            LOGI("------- Detection::detection : det_failed, left_iris_distance_ratio = %.3f!!!------", left_iris_distance_ratio);
            LOGI("------- Detection::detection : det_failed, right_iris_distance_ratio = %.3f!!!------", right_iris_distance_ratio);
        }
    }
    return det_result;
}

void Detection::visual_detection_result_func(cv::Mat &bgr_img)
{
    // ----- 显示检测框 -----
    cv::rectangle(bgr_img, left_eye_rect, Scalar(255, 0, 0), 2);
    cv::rectangle(bgr_img, right_eye_rect, Scalar(0, 255, 255), 2);
    cv::rectangle(bgr_img, left_iris_rect, Scalar(255, 0, 0), 2);
    cv::rectangle(bgr_img, right_iris_rect, Scalar(0, 255, 255), 2);

    // ----- 显示关键点 -----
    circle(bgr_img, left_lights_point_list[0], 5, Scalar(255, 0, 0), 1);
    circle(bgr_img, left_lights_point_list[1], 5, Scalar(0, 255, 255), 1);
    circle(bgr_img, left_pupil_cent_point, 3, Scalar(0, 255, 0), -1);
    circle(bgr_img, right_lights_point_list[0], 5, Scalar(255, 0, 0), 1);
    circle(bgr_img, right_lights_point_list[1], 5, Scalar(0, 255, 255), 1);
    circle(bgr_img, right_pupil_cent_point, 3, Scalar(0, 255, 0), -1);
}
