#ifndef GAZETRACKER_H
#define GAZETRACKER_H

#include "kalmanfilter.h"
#include "gaze_mapping.h"
#include "utils.h"


using namespace cv;
using namespace std;

/**
 * 眼动跟踪
 */
class GazeTracker {

public:
    explicit GazeTracker();
    ~GazeTracker();

private:
    gazePredict left_gaze_predictor;   //【视线预测】左眼
    gazePredict right_gaze_predictor;  // 【视线预测】右眼

    bool calib_param_is_ready;

    CoordinateKmf kmf = CoordinateKmf(4, 2);  // 【视线预测】卡尔曼滤波器

    // gaze result
    Point2d gaze_coord;        //【识别过程】视线坐标
    double pred_distance;      // 用户离屏幕的垂直距离
    double smooth;             //【视线预测】轨迹平滑
    double smooth_inv;
    double k_;                 // Rate of decay of the exponential function

public:
    gaze_point_str predict(const frame_detection_result& det_result);   // 视线坐标映射
    void update_gaze_params(const mapping_params_str& map_params);
    bool get_calib_station_func() const;
    void draw_predict_result_func(cv::Mat &bgr_img);
};

#endif //GAZETRACKER_H
