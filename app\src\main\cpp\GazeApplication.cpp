//
// Created by knight on 2024/12/27.
//

#include "GazeApplication.h"

extern JavaVM *gJvm;
jobject gazeAppliedCallback;

// 宏定义
#define ANATIVEWINDOW_RELEASE(window)   \
if (window) { \
            ANativeWindow_release(window); \
            window = 0; \
      }

GazeApplication::GazeApplication()
{
    app_mode = app_typename::APP_NONE;             // 默认不启动应用
    app_runing = false;
    finish_flag = false;
}

GazeApplication::~GazeApplication()
{
    LOGI("------- destropy GazeApplication!!!! ------");
    pthread_mutex_destroy(&mutex);
    ANATIVEWINDOW_RELEASE(window);
}

/**
 * 当眼动应用模式改变时回调
 * @param mode
 */
void onGazeAppliedModeChange(int mode){
    JNIEnv* env;
    if (gJvm->AttachCurrentThread(&env, nullptr) != JNI_OK) {
        return;
    }

    // 获取Java类及方法的信息
    jclass clazz = env->GetObjectClass(gazeAppliedCallback);
    jmethodID methodId = env->GetMethodID(clazz, "onGazeAppliedModeChange", "(I)V");

    // 调用Java对象的回调方法，传递数值
    env->CallVoidMethod(gazeAppliedCallback, methodId, mode);
}

/**
 * 设置眼动追踪服务回调
 */
void GazeApplication::setGazeAppliedCallback(JNIEnv* env, jobject callback_calib)
{
    LOGE("GazeApplication::setGazeAppliedCallback()");
    // 保存Java对象的引用为全局引用
    gazeAppliedCallback = env->NewGlobalRef(callback_calib);
}

bool GazeApplication::start_application(int mode)
{
    bool flag = false;
    LOGI("------- GazeApplication::start_application app_runing: %d!!!! ------", app_runing);
    if(!app_runing)
    {
        // 在没有运行着程序的情况下
        if(mode >= app_typename::APP_PQBLUR && mode <= app_typename::APP_GLANCE)
        {
            LOGI("------- GazeApplication::start_application mode: %d!!!! ------", mode);
            flag = true;
            app_mode = mode;
            app_runing = true;
            finish_flag = false;
            switch (mode) {
                case app_typename::APP_PQBLUR:
                    // 设置虚化模式
                    pqblur_model.set_red_blur_enable(true);   //打开虚化的开关
                    break;

                case app_typename::APP_READING:
                    // 设置阅读模式
                    reading_model.reset_params_func();             // 阅读训练 初始化参数
                    break;

                case app_typename::APP_STARE:
                    // 设置眼动检查-注视模式
                    stare_model.reset_params_func();               // 眼动检查 —— 注视 初始化参数
                    break;

                case app_typename::APP_FOLLOW:
                    // 设置眼动检查-跟随模式
                    follow_model.reset_params_func();              // 眼动检查 —— 跟随 初始化参数
                    break;

                case app_typename::APP_GLANCE:
                    // 设置眼动检查-扫视模式
                    glance_model.reset_params_func();              // 眼动检查 —— 扫视 初始化参数
                    break;

                default:
                    // 输入的值有误
                    LOGI("------- GazeApplication::start_application wrong input mode: %d!!!! ------", mode);
                    flag = false;
                    app_runing = false;
            }
            onGazeAppliedModeChange(app_mode);
        }
    }
    return flag;
}

bool GazeApplication::stop_application()
{
    bool flag = false;
    if(app_runing)
    {
        app_runing = false;
        finish_flag = true;
        if(app_mode == app_typename::APP_PQBLUR)
        {
            pqblur_model.set_red_blur_enable(false);   //关闭虚化的开关
        }
        app_mode = app_typename::APP_NONE;
        flag = true;
        onGazeAppliedModeChange(app_mode);
    }
    return flag;
}

/* 设置屏幕虚化的参数:  radius: 半径，sigma: 模糊程度，blur_mode: 虚化模式，blur_ch: 虚化通道 */
bool GazeApplication::set_blur_params_func(float radius, float sigma, int blur_mode, int blur_ch)
{
    bool flag = false;
    bool flag1 = pqblur_model.set_red_blur_radius(radius);
    bool flag2 = pqblur_model.set_red_blur_sigma(sigma);
    bool flag3 = pqblur_model.set_red_blur_mode(blur_mode);
    bool flag4 = pqblur_model.set_red_blur_channel(blur_ch);
    if(flag1 && flag2 && flag3 && flag4)
    {
        flag = true;
    }
    return flag;
}

/* 设置注视点 x: 横坐标[0~1]，y:纵坐标[0~1] */
bool GazeApplication::set_stare_point_func(float x, float y)
{
    bool flag = stare_model.set_target_point(x, y);
    return flag;

}

/* 设置扫视的目标点 x: 横坐标[0~1]，y:纵坐标[0~1] */
bool GazeApplication::set_glance_point_func(float x, float y)
{
    bool flag = glance_model.set_target_point(x, y);
    return flag;
}

bool GazeApplication::collect_gaze(bool valid, float x, float y, float dist, float duration)
{
    if(app_runing)
    {
        switch (app_mode) {
            case app_typename::APP_PQBLUR:
                // 虚化模式
                pqblur_model.draw_gaze_result_func(x, y, dist);   // 虚化屏幕位置
                break;

            case app_typename::APP_READING:
                // 设置阅读模式
                reading_model.collect_data(x, y, duration);             // 阅读模式
                break;

            case app_typename::APP_STARE:
                // 设置眼动检查-注视模式
                stare_model.collect_data(x, y, duration);              // 眼动检查 —— 注视
                break;

            case app_typename::APP_FOLLOW:
                // 设置眼动检查-跟随模式
                follow_model.collect_data(x, y, duration);             // 眼动检查 —— 跟随
                break;

            case app_typename::APP_GLANCE:
                // 设置眼动检查-扫视模式
                finish_flag = glance_model.collect_data(x, y, duration);  // 眼动检查 —— 扫视
                break;

            default:
                // 输入的值有误
                LOGI("------- GazeApplication::collect_gaze wrong application mode: %d!!!! ------", app_mode);
        }
    }
    return finish_flag;
}
string GazeApplication::get_record_data_func(int mode)
{
    string result_dict = "";
    switch (mode) {
        case app_typename::APP_READING:
            // 设置阅读模式
            result_dict = reading_model.get_record_jsondata();             // 阅读模式
            break;

        case app_typename::APP_STARE:
            // 设置眼动检查-注视模式
            result_dict = stare_model.get_record_jsondata();              // 眼动检查 —— 注视
            break;

        case app_typename::APP_FOLLOW:
            // 设置眼动检查-跟随模式
            result_dict = follow_model.get_record_jsondata();             // 眼动检查 —— 跟随
            break;

        case app_typename::APP_GLANCE:
            // 设置眼动检查-扫视模式
            result_dict = glance_model.get_record_jsondata();             // 眼动检查 —— 扫视
            break;

        default:
            // 输入的值有误
            LOGI("------- GazeApplication::collect_gaze wrong application mode: %d!!!", mode);
    }
    return result_dict;
}