#ifndef BLUR_H
#define BLUR_H

#include <android/native_window_jni.h>
#include <utility>
#include <pthread.h>
#include <thread>
#include "utils.h"
#include "IPQ_BLR.h"

#define InnerBlur 0
#define OuterBlur 1
#define InnerBlack 2
#define OuterBlack 3

#define InnerGKSize 35
#define OuterGKSize 15

/**
 * 红色通道局部区域模糊
 */
class PqBlur {

public:
    explicit PqBlur(float macular_radius=2.5);
    ~PqBlur();

private:
    // 默认参数
    float default_inner_GKSigma = 10.0F;
    float default_outer_GKSigma = 2.0F;

    int default_pq_radius = 150;
    int default_channel = 1;    // default to red
    int default_mode = InnerBlack;       // 0=InnerBlur，1=OuterBlur，2=InnerBlack，3=OuterBlack

    // gaussian blur for gaze region
    bool blur_enable_flag;           // true -> 虚化视线区域， false -> 关闭虚化功能
    IPQ_BLR* pq_blr;
    int pq_radius;
    float GKSigma;
    /*
    channel：0=None | 1=R | 2=G | 4=B
    mode：0=InnerBlur, 1=OuterBlur, 2=InnerBlack, 3=OuterBlack
    R=1; G=2; B=4;
    设置 RG时 nBlurChannel = R | G = 3；
    设置 RB时 nBlurChannel = R | B = 5；
    设置 GB时 nBlurChannel = G | B = 6；
    设置 RGB时 nBlurChannel = R | G |  B = 7；
    */
    int channel;    // default to red
    int mode;       // 0=InnerBlur，1=OuterBlur，2=InnerBlack，3=OuterBlack

    float macular_blur_radius;   // 黄斑高斯模糊覆盖面积 mm

public:
    void draw_gaze_result_func(float x, float y, float dist);  // 根据视线跟踪结果设置虚化区域
    void set_red_blur_enable(bool valid);  // 视线区域虚化的开关设置
    bool set_red_blur_radius(float macular_radius);  // 视线区域黄斑区的遮盖半径 0.5~5.5
    bool set_red_blur_sigma(float blur_sigma);  // 视线区域的模糊程度，可设置 1 ~ 150, 默认为30
    bool set_red_blur_mode(int blur_mode);      // 屏幕虚化模式，0=InnerBlur，1=OuterBlur，2=InnerBlack，3=OuterBlack
    bool set_red_blur_channel(int blur_ch);     // 屏幕虚化通道，二进制 R=1; G=2; B=4;
};

#endif //BLUR_H
