//
// Created by user on 2024/9/23.
//

#ifndef MIT_DD_DEMO_UPLOADCLOUD_H
#define MIT_DD_DEMO_UPLOADCLOUD_H

#include <alibabacloud/oss/OssClient.h>

#include "utils.h"

using namespace AlibabaCloud::OSS;

class upload2Cloud
{
public:
    upload2Cloud();
    ~upload2Cloud();

    void startUpload2Cloud();

private:
    const string accessKeyId = "LTAI5tEvennsW7Vza8yVzW9G";
    const string accessKeySecret = "******************************";
    const string Endpoint = "http://oss-cn-beijing.aliyuncs.com";
    const string BucketName = "airdoc-mit-dd";
    const string ObjectDir = "EyeTracker/";

    OssClient *client; // OSS客户端类
    ClientConfiguration conf;  //参数配置

    void UploadFileToOSS();
};

#endif //MIT_DD_DEMO_UPLOADCLOUD_H
