//
// Created by knight on 2024/12/29.
//

#ifndef MIT_DD_DEMO_APP_STARE_H
#define MIT_DD_DEMO_APP_STARE_H

#include "utils.h"

using namespace cv;
using namespace std;

#define gaussian_radius 0.1        // 根据注视目标点生成高斯掩模的半径值
#define gaussian_sigma 0.3         // 高斯掩模的方差大小

class GazeStare {
public:
    GazeStare();
    ~GazeStare();

public:
    cv::Point2f target_point;                   // 设置的目标点
    std::vector<cv::Point3f> gaze_data_list;    // 应用期间采集的眼动数据
    cv::Mat gaussian_mask;                      // 对注视结果进行后处理修正的高斯掩模

public:
    void reset_params_func();                                                   // 清空缓存数据
    bool set_target_point(float x, float y);                                    // 设置注视的目标点位置
    void collect_data(float x, float y, float duration);                        // 应用期间传输数据
    std::vector<std::vector<Point3f>> postprocess_trajectory_data();            // 处理眼动轨迹的数据
    string get_record_jsondata();                                               // 获取json的数据
};

#endif //MIT_DD_DEMO_APP_STARE_H
