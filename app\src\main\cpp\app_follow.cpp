//
// Created by user on 2025/1/2.
//

#include "app_follow.h"

GazeFollow::GazeFollow()
{
    gaze_data_list.clear();
}

GazeFollow::~GazeFollow(){}

void GazeFollow::reset_params_func()
{
    gaze_data_list.clear();
}

// 应用期间传输数据
void GazeFollow::collect_data(float x, float y, float duration)
{
    gaze_data_list.emplace_back(Point3f(x, y, duration));
}

// 处理眼动轨迹的数据
std::vector<std::vector<Point3f>> GazeFollow::postprocess_trajectory_data()
{
    std::vector<std::vector<Point3f>> stare_points;
    if(!gaze_data_list.empty())
    {
        int num_p = (int)gaze_data_list.size();
        Point3f pre_p = gaze_data_list[0];
        vector<Point3f> line_poins = {pre_p};
        for (int i = 1; i < num_p; i++)
        {
            Point3f p1 = gaze_data_list[i - 1];
            Point3f p2 = gaze_data_list[i];
            float grad1 = abs(p2.x - p1.x) + abs(p2.y - p1.y);
            float grad2 = abs(p2.x - pre_p.x) + abs(p2.y - pre_p.y);
            if(grad1 < app_filter_thres)
            {
                if (grad2 < app_merge_thres)
                {
                    line_poins.push_back(p2);
                }
                else
                {
                    pre_p = p2;
                    stare_points.push_back(line_poins);
                    line_poins.clear();
                    line_poins.push_back(pre_p);
                }
            }
        }
    }
    return stare_points;
}

// 获取json的数据
string GazeFollow::get_record_jsondata()
{
    string json_data = "{'gaze': []}";
    std::vector<std::vector<Point3f>> stare_points = postprocess_trajectory_data();
    if(!stare_points.empty())
    {
        json_data = transfer_trajectory_to_jsondata(stare_points);
    }
    return json_data;
}