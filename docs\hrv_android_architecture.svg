<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 9px; fill: #7f8c8d; }
      .bridge-detail { font-family: Arial, sans-serif; font-size: 10px; fill: #d35400; font-weight: bold; }
      .android-layer { fill: #a8e6cf; stroke: #27ae60; stroke-width: 2; }
      .webview-layer { fill: #ffd3a5; stroke: #f39c12; stroke-width: 2; }
      .web-layer { fill: #e8e8e8; stroke: #95a5a6; stroke-width: 2; }
      .hardware-layer { fill: #ffaaa5; stroke: #e74c3c; stroke-width: 2; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #e67e22; stroke-width: 2; fill: none; stroke-dasharray: 5,5; }
      .bridge-arrow { stroke: #d35400; stroke-width: 3; fill: none; marker-end: url(#bridgehead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
    <marker id="bridgehead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#d35400" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="450" y="30" text-anchor="middle" class="title">安卓端在HRV检测中的架构作用</text>
  
  <!-- Android Native Layer -->
  <rect x="50" y="60" width="800" height="120" class="android-layer" rx="10"/>
  <text x="70" y="85" class="subtitle">Android Native Layer (原生安卓层)</text>

  <!-- HrvActivity -->
  <rect x="70" y="100" width="150" height="60" fill="#ffffff" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="145" y="120" text-anchor="middle" class="text">HrvActivity</text>
  <text x="145" y="135" text-anchor="middle" class="small-text">• 生命周期管理</text>
  <text x="145" y="150" text-anchor="middle" class="small-text">• 用户界面容器</text>

  <!-- DetectionActivity -->
  <rect x="240" y="100" width="150" height="60" fill="#ffffff" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="315" y="120" text-anchor="middle" class="text">DetectionActivity</text>
  <text x="315" y="135" text-anchor="middle" class="small-text">• 检测项目路由</text>
  <text x="315" y="150" text-anchor="middle" class="small-text">• 用户信息管理</text>

  <!-- Permission Manager -->
  <rect x="410" y="100" width="150" height="60" fill="#ffffff" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="485" y="120" text-anchor="middle" class="text">权限管理</text>
  <text x="485" y="135" text-anchor="middle" class="small-text">• 摄像头权限</text>
  <text x="485" y="150" text-anchor="middle" class="small-text">• 设备访问控制</text>

  <!-- Print Manager -->
  <rect x="580" y="100" width="150" height="60" fill="#ffffff" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="655" y="120" text-anchor="middle" class="text">原生功能</text>
  <text x="655" y="135" text-anchor="middle" class="small-text">• 打印报告</text>
  <text x="655" y="150" text-anchor="middle" class="small-text">• 导航控制</text>

  <!-- User Info -->
  <rect x="750" y="100" width="80" height="60" fill="#ffffff" stroke="#27ae60" stroke-width="1" rx="5"/>
  <text x="790" y="120" text-anchor="middle" class="text">用户信息</text>
  <text x="790" y="135" text-anchor="middle" class="small-text">• Token管理</text>
  <text x="790" y="150" text-anchor="middle" class="small-text">• 身份验证</text>
  
  <!-- WebView Bridge Layer -->
  <rect x="50" y="200" width="800" height="180" class="webview-layer" rx="10"/>
  <text x="70" y="225" class="subtitle">WebView Bridge Layer (WebView桥接层) - 详细操作</text>

  <!-- HrvWebView 初始化 -->
  <rect x="70" y="240" width="180" height="80" fill="#ffffff" stroke="#f39c12" stroke-width="1" rx="5"/>
  <text x="160" y="255" text-anchor="middle" class="text">HrvWebView初始化</text>
  <text x="160" y="270" text-anchor="middle" class="small-text">• 继承CustomWebView</text>
  <text x="160" y="280" text-anchor="middle" class="small-text">• 设置WebViewClient</text>
  <text x="160" y="290" text-anchor="middle" class="small-text">• 设置WebChromeClient</text>
  <text x="160" y="300" text-anchor="middle" class="small-text">• 配置JavaScript支持</text>
  <text x="160" y="310" text-anchor="middle" class="small-text">• 注册HrvAction接口</text>

  <!-- WebView 配置操作 -->
  <rect x="270" y="240" width="180" height="80" fill="#ffffff" stroke="#f39c12" stroke-width="1" rx="5"/>
  <text x="360" y="255" text-anchor="middle" class="text">WebView配置操作</text>
  <text x="360" y="270" text-anchor="middle" class="small-text">• MIXED_CONTENT_ALWAYS_ALLOW</text>
  <text x="360" y="280" text-anchor="middle" class="small-text">• LOAD_NO_CACHE缓存策略</text>
  <text x="360" y="290" text-anchor="middle" class="small-text">• 透明背景设置</text>
  <text x="360" y="300" text-anchor="middle" class="small-text">• 权限自动授权</text>
  <text x="360" y="310" text-anchor="middle" class="small-text">• 生命周期绑定</text>

  <!-- JavaScript 桥接操作 -->
  <rect x="470" y="240" width="180" height="80" fill="#ffffff" stroke="#f39c12" stroke-width="1" rx="5"/>
  <text x="560" y="255" text-anchor="middle" class="text">JavaScript桥接操作</text>
  <text x="560" y="270" text-anchor="middle" class="bridge-detail">@JavascriptInterface</text>
  <text x="560" y="280" text-anchor="middle" class="small-text">• onFinish() → 关闭页面</text>
  <text x="560" y="290" text-anchor="middle" class="small-text">• goHome() → 返回主页</text>
  <text x="560" y="300" text-anchor="middle" class="small-text">• printPage() → 打印报告</text>
  <text x="560" y="310" text-anchor="middle" class="small-text">• ready() → 页面就绪</text>

  <!-- 事件监听处理 -->
  <rect x="670" y="240" width="160" height="80" fill="#ffffff" stroke="#f39c12" stroke-width="1" rx="5"/>
  <text x="750" y="255" text-anchor="middle" class="text">事件监听处理</text>
  <text x="750" y="270" text-anchor="middle" class="small-text">• onPageFinished监听</text>
  <text x="750" y="280" text-anchor="middle" class="small-text">• 权限请求处理</text>
  <text x="750" y="290" text-anchor="middle" class="small-text">• 生命周期回调</text>
  <text x="750" y="300" text-anchor="middle" class="small-text">• 错误处理机制</text>
  <text x="750" y="310" text-anchor="middle" class="small-text">• 状态同步</text>

  <!-- 桥接操作流程箭头 -->
  <line x1="250" y1="280" x2="270" y2="280" class="bridge-arrow"/>
  <line x1="450" y1="280" x2="470" y2="280" class="bridge-arrow"/>
  <line x1="650" y1="280" x2="670" y2="280" class="bridge-arrow"/>

  <!-- 桥接层核心功能说明 -->
  <rect x="70" y="330" width="760" height="40" fill="#fff3cd" stroke="#f39c12" stroke-width="1" rx="5"/>
  <text x="80" y="345" class="bridge-detail">桥接层核心功能：</text>
  <text x="80" y="355" class="small-text">1. 将Web页面的JavaScript调用转换为Android原生操作  2. 管理WebView生命周期和配置  3. 处理权限授权和事件回调</text>
  <text x="80" y="365" class="small-text">4. 提供Web与Android双向通信通道  5. 确保HRV检测过程的稳定性和用户体验</text>
  
  <!-- Web Layer -->
  <rect x="50" y="320" width="700" height="100" class="web-layer" rx="10"/>
  <text x="70" y="345" class="subtitle">Web Layer (Web层 - HRV核心算法)</text>
  
  <!-- HRV Algorithm -->
  <rect x="70" y="360" width="150" height="50" fill="#ffffff" stroke="#3498db" stroke-width="1" rx="5"/>
  <text x="145" y="380" text-anchor="middle" class="text">HRV算法</text>
  <text x="145" y="395" text-anchor="middle" class="small-text">• 心率变异性分析</text>
  
  <!-- PPG Processing -->
  <rect x="240" y="360" width="150" height="50" fill="#ffffff" stroke="#3498db" stroke-width="1" rx="5"/>
  <text x="315" y="380" text-anchor="middle" class="text">PPG信号处理</text>
  <text x="315" y="395" text-anchor="middle" class="small-text">• 光电容积脉搏波</text>
  
  <!-- Data Analysis -->
  <rect x="410" y="360" width="150" height="50" fill="#ffffff" stroke="#3498db" stroke-width="1" rx="5"/>
  <text x="485" y="380" text-anchor="middle" class="text">数据分析</text>
  <text x="485" y="395" text-anchor="middle" class="small-text">• 频域/时域分析</text>
  
  <!-- Report Generation -->
  <rect x="580" y="360" width="150" height="50" fill="#ffffff" stroke="#3498db" stroke-width="1" rx="5"/>
  <text x="655" y="380" text-anchor="middle" class="text">报告生成</text>
  <text x="655" y="395" text-anchor="middle" class="small-text">• 结果可视化</text>
  
  <!-- Hardware Layer -->
  <rect x="50" y="440" width="700" height="80" class="hardware-layer" rx="10"/>
  <text x="70" y="465" class="subtitle">Hardware Layer (硬件层)</text>
  
  <!-- Camera -->
  <rect x="200" y="480" width="120" height="30" fill="#ffffff" stroke="#e74c3c" stroke-width="1" rx="5"/>
  <text x="260" y="500" text-anchor="middle" class="text">摄像头</text>
  
  <!-- Flash -->
  <rect x="340" y="480" width="120" height="30" fill="#ffffff" stroke="#e74c3c" stroke-width="1" rx="5"/>
  <text x="400" y="500" text-anchor="middle" class="text">闪光灯</text>
  
  <!-- Sensors -->
  <rect x="480" y="480" width="120" height="30" fill="#ffffff" stroke="#e74c3c" stroke-width="1" rx="5"/>
  <text x="540" y="500" text-anchor="middle" class="text">传感器</text>
  
  <!-- 箭头和数据流 -->
  <!-- Android to WebView -->
  <line x1="145" y1="160" x2="170" y2="240" class="arrow"/>
  <line x1="315" y1="160" x2="400" y2="240" class="arrow"/>
  <line x1="485" y1="160" x2="630" y2="240" class="arrow"/>
  
  <!-- WebView to Web -->
  <line x1="170" y1="290" x2="145" y2="360" class="arrow"/>
  <line x1="400" y1="290" x2="315" y2="360" class="arrow"/>
  <line x1="630" y1="290" x2="655" y2="360" class="arrow"/>
  
  <!-- Hardware to Web -->
  <line x1="260" y1="480" x2="315" y2="410" class="data-flow"/>
  <line x1="400" y1="480" x2="315" y2="410" class="data-flow"/>
  
  <!-- 数据流标注 -->
  <text x="280" y="450" class="small-text">摄像头数据流</text>
  <text x="50" y="550" class="small-text">说明：实线箭头表示控制流，虚线表示数据流</text>
  
  <!-- 功能说明框 -->
  <rect x="520" y="540" width="250" height="50" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="530" y="560" class="text">安卓端主要作用：</text>
  <text x="530" y="575" class="small-text">1. 提供硬件访问和权限管理</text>
  <text x="530" y="585" class="small-text">2. 作为Web算法的容器和桥接</text>
</svg>
