#include "gaze_mapping.h"

gazePredict::gazePredict()
{
    model_isready = false;
}
gazePredict::~gazePredict()
{
}

void gazePredict::load_model_param(const vector<std::vector<double>>& calib_param)
{
    if (checkVectorDimensions(calib_param)) {
        model_param = calib_param;
        model_isready = true;
        LOGI("The vector dimensions match the expected pattern.");
    } else {
        model_isready = false;
        LOGI("The vector dimensions do not match the expected pattern.");
    }
}

void gazePredict::predict(const Point2f& pupil_cent, const vector<Point2f>& point_crs, double eye_dist, Point2d& screen)
{
    if(model_isready)
    {
        vector<bool> valid_list;
        calculate_2points_state(point_crs, valid_list);
        vector<double> screen_x_vec;
        vector<double> screen_y_vec;

        LOGI("*** gazePredict::predict valid_list = {%d, %d}.***", (int)valid_list[0], (int)valid_list[1]);

        // two point valid
        if (valid_list[0] && valid_list[1])
        {
            LOGI("gazePredict::predict  predict by two point");
            Point2d crs_cent;
            crs_cent.x = (double)(point_crs[0].x + point_crs[1].x) / 2.0;
            crs_cent.y = (double)(point_crs[0].y + point_crs[1].y) / 2.0;
            double crs_dist = std::sqrtf(std::powf((float)point_crs[0].x - (float)point_crs[1].x, 2) + powf((float)point_crs[0].y - (float)point_crs[1].y, 2));

            // cout << "crs_dist = " << crs_dist << endl;
            double glx = crs_cent.x - (double)pupil_cent.x;
            double gly = crs_cent.y - (double)pupil_cent.y;

            double norm_glx = glx / crs_dist;
            double norm_gly = gly / crs_dist;

            std::vector<double> params_x_c = model_param[0];
            std::vector<double> params_y_c = model_param[1];
            // cout << "prepare to calculate" << endl;
            if (!params_x_c.empty() && !params_y_c.empty())
            {
                double c_screen_x = params_x_c[0] * norm_glx + params_x_c[1] * norm_gly + params_x_c[2] * norm_glx * norm_glx + params_x_c[3];
                double c_screen_y = params_y_c[0] * norm_glx + params_y_c[1] * norm_gly + params_y_c[2] * norm_gly * norm_gly + params_y_c[3];
                // cout << "c_screen_x = " << c_screen_x << endl;
                // cout << "c_screen_y = " << c_screen_y << endl;
                c_screen_x = std::max(0.0, std::min(1.0, c_screen_x));
                c_screen_y = std::max(0.0, std::min(1.0, c_screen_y));
                screen_x_vec.push_back(c_screen_x);
                screen_y_vec.push_back(c_screen_y);
                LOGI("*** gazePredict::predict c_screen_x = %f,  c_screen_y = %f.***", c_screen_x, c_screen_y);
            }
            // cout << "finish predict" << endl;
        }
        LOGI("gazePredict::predict  predict by single point");
        // left point
        if (valid_list[0] && eye_dist > pose_align_interpupillary_distance_min)
        {
            LOGI("gazePredict::predict  predict by left point");
            double lx = (double)(point_crs[0].x - pupil_cent.x) / eye_dist;
            double ly = (double)(point_crs[0].y - pupil_cent.y) / eye_dist;
            std::vector<double> params_x_l = model_param[2];
            std::vector<double> params_y_l = model_param[3];
            if (!params_x_l.empty() && !params_y_l.empty())
            {
                double l_screen_x = params_x_l[0] * lx + params_x_l[1] * ly + params_x_l[2] * lx * lx + params_x_l[3];
                double l_screen_y = params_y_l[0] * lx + params_y_l[1] * ly + params_y_l[2] * ly * ly + params_y_l[3];
                // cout << "l_screen_x = " << l_screen_x << endl;
                // cout << "l_screen_y = " << l_screen_y << endl;
                l_screen_x = std::max(0.0, std::min(1.0, l_screen_x));
                l_screen_y = std::max(0.0, std::min(1.0, l_screen_y));
                screen_x_vec.push_back(l_screen_x);
                screen_y_vec.push_back(l_screen_y);
                LOGI("*** gazePredict::predict l_screen_x = %f,  l_screen_y = %f.***", l_screen_x, l_screen_y);
            }
        }

        // right point
        if (valid_list[1] && eye_dist > pose_align_interpupillary_distance_min)
        {
            LOGI("gazePredict::predict  predict by right point");
            double rx = (double)(point_crs[1].x - pupil_cent.x) / eye_dist;
            double ry = (double)(point_crs[1].y - pupil_cent.y) / eye_dist;
            std::vector<double> params_x_r = model_param[4];
            std::vector<double> params_y_r = model_param[5];
            if (!params_x_r.empty() && !params_y_r.empty())
            {
                // feature = [x, y, x * y, x * x, y * y]
                double r_screen_x = params_x_r[0] * rx + params_x_r[1] * ry + params_x_r[2] * rx * rx + params_x_r[3];
                double r_screen_y = params_y_r[0] * rx + params_y_r[1] * ry + params_y_r[2] * ry * ry + params_y_r[3];
                // cout << "r_screen_x = " << r_screen_x << endl;
                // cout << "r_screen_y = " << r_screen_y << endl;
                r_screen_x = std::max(0.0, std::min(1.0, r_screen_x));
                r_screen_y = std::max(0.0, std::min(1.0, r_screen_y));
                screen_x_vec.push_back(r_screen_x);
                screen_y_vec.push_back(r_screen_y);
                LOGI("*** gazePredict::predict r_screen_x = %f,  r_screen_y = %f.***", r_screen_x, r_screen_y);
            }
        }

        // compute screen point
        if (!screen_x_vec.empty() && !screen_y_vec.empty())
        {
            double sum_x = 0;
            double sum_y = 0;

            for (size_t i = 0; i < screen_x_vec.size(); ++i)
            {
                sum_x += screen_x_vec[i];
                sum_y += screen_y_vec[i];
            }
            screen.x = sum_x / (double)screen_x_vec.size();
            screen.y = sum_y / (double)screen_y_vec.size();
        }
        else
        {
            screen.x = -1;
            screen.y = -1;
        }
    }
    else
    {
        LOGI("*** gazePredict::predict model is not ready.***");
        // 未检测到有效的校准参数
        screen.x = -1;
        screen.y = -1;
    }
    // cout << "screen = " << screen << endl;
}
