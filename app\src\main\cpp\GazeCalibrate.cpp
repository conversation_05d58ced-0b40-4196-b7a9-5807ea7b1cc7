#include "GazeCalibrate.h"

GazeCalibrate::GazeCalibrate() : calibration_class(point_index_order, calibrate_points) {
    LOGI("------- created GazeCalibrate instance!!!! ------");
    reset_parameters();
}

GazeCalibrate::~GazeCalibrate() = default;

/**
 * 初始化参数
 */
void GazeCalibrate::reset_parameters()
{
    // init result
    visual_calib_img = Mat(visual_image_height, visual_image_width, CV_8UC3, Scalar(255, 255, 255));
    eye_consist_num = {0, 0};  //双眼校准的进程 {0, 1, 2, 3}

    // event state
    calib_flag = false;
    calibrate_next_point = false;
    finish_calibration = false;
    calibrate_succeed = false;      //单个目标点是否校准成功

    capture_index = 0;

    for (bool & i : succeed_list) {
        i = false;
    }

    decimal_left = 0;
    decimal_right = 0;

    calibrate_data_params.clear();

    capture_delay_time = Clock::now();
    record_calib_time = Clock::now();
}

/**
 * 启动校准标志，初始化参数
 */
void GazeCalibrate::start_calibration() {
    // start calibrating
    reset_parameters();
    calib_flag = true;                              // 开始校准
    calibration_class.reset_calibrate_params();
    calibrate_next_point = true;                    // begin to calibrate first point
}

/**
 * 中途关闭校准，初始化参数
 */
void GazeCalibrate::stop_calibration() {
    // close pose_align and calibrating
    reset_parameters();
}

calibrating_result_str GazeCalibrate::run_calibration_func(const frame_detection_result& det_result, const std::function<void(bool, bool, float, int , int, float, float, int)>& callback_calib)
{
    calibrating_result_str calib_result;
    bool finish_calib = false;
    if(calib_flag) {
        LOGI("------- run_calibration_func: calibrating!!!! ------");
        // --- 调用校准处理函数 ---
        if(!calibrate_next_point && !finish_calibration)
        {
            LOGI("------- run_calibration_func: calibration_class.run_calibrating!!!! ------");
            eye_consist_num = calibration_class.run_calibrating(det_result, calibrate_next_point, finish_calibration, calibrate_succeed, succeed_list);
            if(calibrate_next_point)
            {
                capture_index++;  // next point
            }

            // 校准完成并且校准成功，读取校准特征数据及其映射参数值
            if(finish_calibration && calibrate_succeed)
            {
                calibrate_data_params = calibration_class.get_calibrate_data_and_params_func();
            }
        }

        // ---  完成单调校准或完成全部校准 ---
        if (calibrate_next_point || finish_calibration) {
            // ----- 完成一个点的校准 ------
            LOGI("------- run_calibration_func: calibrate_next_point: %d,  finish_calibration: %d !!!! ------", calibrate_next_point, finish_calibration);
            int p_idx = point_index_order[capture_index];
            int label_x = (int) (calibrate_points[p_idx][0] * (float) visual_image_width);
            int label_y = (int) (calibrate_points[p_idx][1] * (float) visual_image_height);
            // 调用回调函数，将结果传递给Java层
            float calib_score = calibration_class.get_calibrate_score_func();
            boolVectorToDecimal(succeed_list, calibration_number, decimal_left, decimal_right);
            if (callback_calib != nullptr) {
                callback_calib(finish_calibration, calibrate_succeed, calib_score,
                               decimal_left, decimal_right,
                               calibrate_points[p_idx][0], calibrate_points[p_idx][1], p_idx);
            }
            if (calibrate_next_point) {
                calibrate_next_point = false;
                bool setup_flag = calibration_class.setup_calibrate_point_state(capture_index);  // begin to calibration point
                if (setup_flag) {
                    draw_target_points(capture_index);
                    LOGI("------- setup calibrate point (%d, %d) succeed!!!! ------", label_x, label_y);
                } else {
                    LOGI("++++++++ setup calibrate point (%d, %d) failed!!!! +++++++", label_x, label_y);
                }
            } else {
                draw_calibration_result();  //显示校准效果
                calib_flag = false;         // finish calibration and break function
                finish_calib = true;
            }
        }
    }
    else
    {
        // 没有启动重置校准参数的命令
        finish_calib = true;
    }
    calib_result.finish_flag = finish_calib;
    calib_result.left_consist_num = eye_consist_num[0];
    calib_result.right_consist_num = eye_consist_num[1];

    return calib_result;
}

void GazeCalibrate::draw_calibration_result()
{
    visual_calib_img = Mat(visual_image_height, visual_image_width, CV_8UC3, Scalar(255, 255, 255));
    float calib_score = calibration_class.get_calibrate_score_func();
    char text[256];
    snprintf(text, 256, "%f", calib_score);
    cv::putText(visual_calib_img, text, cv::Point(900, 300), cv::FONT_HERSHEY_SIMPLEX, 5, Scalar(0, 255, 255), 3);
    for (int i=0; i < calibration_number; i++)
    {
        int lidx = 2 * i;
        int ridx = 2 * i + 1;
        bool left_flag = succeed_list[lidx];
        bool right_flag = succeed_list[ridx];

        float x = calibrate_points[i][0];
        float y = calibrate_points[i][1];

        Point label_left = cv::Point((int)(x * (float)visual_image_width) - 31, (int)(y * (float)visual_image_height));
        Point label_right = cv::Point((int)(x * (float)visual_image_width) + 31, (int)(y * (float)visual_image_height));
        // left point
        if(left_flag)
        {
            circle(visual_calib_img, label_left, 27, Scalar(0, 255, 0), -1);   // outer
        }
        else
        {
            circle(visual_calib_img, label_left, 27, Scalar(0, 0, 255), -1);   // outer
        }
        // right point
        if(right_flag)
        {
            circle(visual_calib_img, label_right, 27, Scalar(0, 255, 0), -1);   // outer
        }
        else
        {
            circle(visual_calib_img, label_right, 27, Scalar(0, 0, 255), -1);   // outer
        }
    }
}

void GazeCalibrate::draw_target_points(int index)
{
    int p_idx = point_index_order[index];
    float x = calibrate_points[p_idx][0];
    float y = calibrate_points[p_idx][1];

    Point label = cv::Point((int)(x * (float)visual_image_width), (int)(y * (float)visual_image_height));
    visual_calib_img = Mat(visual_image_height, visual_image_width, CV_8UC3, Scalar(255, 255, 255));
    circle(visual_calib_img, label, 31, Scalar(205, 116, 24), -1);   // outer
    circle(visual_calib_img, label, 15, Scalar(0, 165, 255), -1);    // inter
    LOGI("------- GazeCalibrate: draw_target_points: (%d, %d) !!!! ------", label.x, label.y);
}

bool GazeCalibrate::get_calib_parameters_data(calibration_params_data_str& calib_datas)
{
    if(calibrate_succeed)
    {
        calib_datas = calibrate_data_params;
    }
    return calibrate_succeed;
}


