//
// Created by user on 2024/9/23.
//
#include "uploadCloud.h"

upload2Cloud::upload2Cloud()
{
    client = new OssClient(Endpoint, accessKeyId, accessKeySecret, conf);
}

upload2Cloud::~upload2Cloud()
{
    delete client;
}


void upload2Cloud::startUpload2Cloud()
{
    LOGI("upload2Cloud begin to run UploadFileToOSS ...");
    // 使用std::async来异步执行上传操作
    UploadFileToOSS();
    LOGI("upload2Cloud finish UploadFileToOSS...");
}

// 上传文件到OSS的函数
void upload2Cloud::UploadFileToOSS() {
    /* 初始化网络等资源。*/
    InitializeSdk();
    // 罗列文件夹的数据
    vector<std::string> filepathList;
    const char *filePath = store_dir;
    ListFilesInDirectory(filePath, filepathList);
    for(const auto& file_ : filepathList)
    {
        /* 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。*/
        std::string Objectfile = ObjectDir + file_;
        std::string Localfile = filePath + file_;

        /* 填写本地文件完整路径，例如D:\\localpath\\examplefile.txt，其中localpath为本地文件examplefile.txt所在本地路径。*/
        std::shared_ptr<std::iostream> content = std::make_shared<std::fstream>(Localfile, std::ios::in | std::ios::binary);
        PutObjectRequest request(BucketName, Objectfile, content);
        auto outcome = client->PutObject(request);

        if (!outcome.isSuccess()) {
            /* 异常处理。*/
            LOGE("file: upload2Cloud,  func: UploadFileToOSS, reply: PutObject fail!! "
                 "code: %s,  message: %s, requestId: %s  \n", outcome.error().Code().c_str(),
                 outcome.error().Message().c_str(), outcome.error().RequestId().c_str());
             remove(Localfile.c_str()); // 上传失败也删除文件，避免积压
        }
        else
        {
            LOGI("file: upload2Cloud,  func: UploadFileToOSS delete files: %s...", Localfile.c_str());
            /*上传完成后，删除源文件*/
             remove(Localfile.c_str()); // 删除文件
        }
    }
    /* 释放网络等资源。*/
    ShutdownSdk();
}







