#ifndef GAZECALIB_H
#define GAZECALIB_H

#include "calibration.h"
#include "utils.h"

using namespace cv;
using namespace std;
using namespace chrono;

typedef std::chrono::high_resolution_clock Clock;

/**
 * 眼动校准
 */
class GazeCalibrate {

public:
    GazeCalibrate();
    ~GazeCalibrate();

public:
    Mat visual_calib_img;           // 显示眼动校准过程
    vector<int> eye_consist_num;    //双眼校准的进程 {0, 1, 2, 3}

    // 校准点坐标以及顺序
    int point_index_order[calibration_number] = {4, 5, 8, 7, 6, 3 , 0, 1, 2};
    float calibrate_points[calibration_number][2] = {{0.1, 0.1}, {0.5, 0.1}, {0.9, 0.1},
                                                     {0.1, 0.5}, {0.5, 0.5}, {0.9, 0.5},
                                                     {0.1, 0.9}, {0.5, 0.9}, {0.9, 0.9}};

    Calibrator calibration_class;

private:
    // time clock
    chrono::time_point<steady_clock, nanoseconds> capture_delay_time;    // 【校准过程】两帧之间的延迟
    chrono::time_point<steady_clock, nanoseconds> record_calib_time;     // 【校准过程】计时

    // calibration
    bool calib_flag;
    bool calibrate_next_point;
    bool finish_calibration;
    bool calibrate_succeed;
    int capture_index;

    bool succeed_list[calibration_number * 2];
    int decimal_left;                 //  对succeed_list数组进行2进制编码后的10进制整数值
    int decimal_right;

    calibration_params_data_str calibrate_data_params;        // 校准过程中的双眼的9点采样数据及其映射参数值

    void reset_parameters();
public:
    void draw_calibration_result();        // 显示9点校准的结果
    void draw_target_points(int index);    // 显示当前校准点

    void start_calibration();
    void stop_calibration();
    calibrating_result_str run_calibration_func(const frame_detection_result& det_result,
                                                const std::function<void(bool, bool, float, int ,
                                                        int, float, float, int)>& callback_calib);

    bool get_calib_parameters_data(calibration_params_data_str& calib_datas);
};



#endif //GAZECALIB_H
