//
// Created by user on 2024/12/17.
//

#ifndef MIT_DD_DEMO_SAVEVIDEOS_H
#define MIT_DD_DEMO_SAVEVIDEOS_H

#include "utils.h"

using namespace cv;
using namespace std;

using std::string;

#define video_face_img_w 256
#define video_face_img_h 192
#define video_eye_img_w 640
#define video_eye_img_h 480

/**
 * 眼动校准
 */
class SaveVideos {

public:
    explicit SaveVideos();
    ~SaveVideos();

private:
    string prefix_name;
    std::vector<std::vector<Point2f>> params_matrix; // 定义一个空的二维向量保存视频参数值
    Point left_save_iris_p;      // 保存左眼视频的中心点位置
    Point right_save_iris_p;     // 保存右眼视频的中心点位置

    bool save_flag;              //开启数据保存的标志

    // 保存视频参数
    char face_video_filename[128];
    char left_video_filename[128];
    char right_video_filename[128];
    char video_params_filename[128];
    char calib_debug_params_filename[128];

    VideoWriter face_outputVideo;
    VideoWriter left_outputVideo;
    VideoWriter right_outputVideo;

public:
    void start_saving_videos(string& pre_name);
    bool stop_saving_videos(const calibration_params_data_str& calib_datas);
    void save_videos_func(const cv::Mat &Image, const frame_detection_result& det_result);       // 根据检测结果保存视频数据

private:
    void create_video_filename();
    void create_videoCapture();
    void close_videoCapture();
    void face_videoWrite(Mat& image);
    void left_videoWrite(Mat& image, Point& iris_cent);
    void right_videoWrite(Mat& image, Point& iris_cent);
};

#endif //MIT_DD_DEMO_SAVEVIDEOS_H
