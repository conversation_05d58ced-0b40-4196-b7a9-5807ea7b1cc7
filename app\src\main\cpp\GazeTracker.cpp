#include "GazeTracker.h"

GazeTracker::GazeTracker(){
    left_gaze_predictor = gazePredict();     // 初始化左眼跟踪器
    right_gaze_predictor = gazePredict();    // 初始化右眼跟踪器
    calib_param_is_ready = false;
    gaze_coord = cv::Point2d(0, 0);
    smooth = 0.6;
    smooth_inv = 0.4;
    k_ = -std::log(0.01);

    pred_distance = -1.0;
}

GazeTracker::~GazeTracker() = default;

/**
 * 查询初始的映射参数状态
 */
bool GazeTracker::get_calib_station_func() const
{
    return calib_param_is_ready;
}

/**
 * 读取本地的眼动参数文件
 */
void GazeTracker::update_gaze_params(const mapping_params_str& map_params)
{
    // ** 加载校准参数 **
    if(map_params.valid)
    {
        left_gaze_predictor.load_model_param(map_params.left_params);       // 预测模块加载校准后的映射参数
        right_gaze_predictor.load_model_param(map_params.right_params);
        calib_param_is_ready = true;
    }
}

gaze_point_str GazeTracker::predict(const frame_detection_result& det_result) {
    LOGI("------- GazeTracker: predict begin---------");
    // --- predict from two eye predictor ---
    Point2d left_pred_gaze = Point2d(-1, -1);
    Point2d right_pred_gaze = Point2d(-1, -1);
    if(calib_param_is_ready)
    {
        if (det_result.left_eye.valid) {
            // 识别左眼
            vector<Point2f> left_lights_point_list = {det_result.left_eye.light_left, det_result.left_eye.light_right};
            left_gaze_predictor.predict(det_result.left_eye.pupil, left_lights_point_list, det_result.interpupillary_distance, left_pred_gaze);
            LOGI("------- GazeTracker left_gaze_predictor.predict: left_pred_gaze = (%f, %f) ------", left_pred_gaze.x, left_pred_gaze.y);
        }
        if (det_result.right_eye.valid) {
            // 识别右眼
            vector<Point2f> right_lights_point_list = {det_result.right_eye.light_left, det_result.right_eye.light_right};
            right_gaze_predictor.predict(det_result.right_eye.pupil, right_lights_point_list, det_result.interpupillary_distance, right_pred_gaze);
            LOGI("------- GazeTracker right_gaze_predictor.predict: right_pred_gaze = (%f, %f) ------", right_pred_gaze.x, right_pred_gaze.y);
        }

        // --- predict gaze strategy ---
        Point2d ori_pred_gaze = Point2d(-1, -1);
        if (left_pred_gaze.x >= 0 && right_pred_gaze.x >= 0 && left_pred_gaze.y >= 0 && right_pred_gaze.y >= 0) {
            // two eye
            ori_pred_gaze.x = (left_pred_gaze.x + right_pred_gaze.x) / 2;
            ori_pred_gaze.y = (left_pred_gaze.y + right_pred_gaze.y) / 2;
            // cout << "use tow eye result" << endl;
        } else {
            if (left_pred_gaze.x >= 0 && left_pred_gaze.y >= 0) {
                // left eye
                ori_pred_gaze.x = left_pred_gaze.x;
                ori_pred_gaze.y = left_pred_gaze.y;
                // cout << "use left eye result" << endl;
            } else {
                if (right_pred_gaze.x >= 0 && right_pred_gaze.y >= 0) {
                    // riht eye
                    ori_pred_gaze.x = right_pred_gaze.x;
                    ori_pred_gaze.y = right_pred_gaze.y;
                    // cout << "use riht eye result" << endl;
                }
            }
        }

        // --- kalmanfilter ---
        if (ori_pred_gaze.x >= 0 && ori_pred_gaze.y >= 0) {
            // kmf.update((float) ori_pred_gaze.x, (float) ori_pred_gaze.y);
            double dist = std::abs(ori_pred_gaze.x - gaze_coord.x) + std::abs(ori_pred_gaze.y - gaze_coord.y);
            smooth = std::exp(-k_ * dist);
            smooth_inv = 1.0 - smooth;
            gaze_coord.x = smooth * gaze_coord.x + smooth_inv * ori_pred_gaze.x;
            gaze_coord.y = smooth * gaze_coord.y + smooth_inv * ori_pred_gaze.y;
            LOGI("------- GazeTracker predict: predict_gaze = (%f, %f) ------", gaze_coord.x, gaze_coord.y);
        }

        // --- 计算距离值 ---
        double sum_iris_width = 0;
        double mean_iris_width = -1;
        int valid_n = 0;
        if(det_result.left_eye.valid)
        {
            sum_iris_width += (double)det_result.left_eye.iris_rect.width;
            valid_n++;
        }
        if(det_result.right_eye.valid)
        {
            sum_iris_width += (double)det_result.right_eye.iris_rect.width;
            valid_n++;
        }
        if (valid_n > 0)
        {
            mean_iris_width = sum_iris_width / (double)valid_n;
            double distance = map_iris_len_pixel_to_dist_func(mean_iris_width);
            if(pred_distance > 0)
            {
                pred_distance = pred_distance * 0.5 + distance * 0.5;
            }
            else
            {
                pred_distance = distance;
            }
        }
    }

    gaze_point_str gaze_result(gaze_coord.x, gaze_coord.y, pred_distance);
    return gaze_result;
}

void GazeTracker::draw_predict_result_func(cv::Mat &bgr_img)
{
    if(gaze_coord.x >= 0 && gaze_coord.y >= 0)
    {
        int sx = (int)(gaze_coord.x * (double)visual_image_width);
        int sy = (int)(gaze_coord.y * (double)visual_image_height);
        circle(bgr_img, cv::Point(sx, sy), 31, Scalar(0, 255, 255), -1);
    }
}


