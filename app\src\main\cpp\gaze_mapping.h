#ifndef GAZE_MAPPING_H
#define GAZE_MAPPING_H

#include "utils.h"

class gazePredict
{
    public:
        gazePredict();
        ~gazePredict();
        void load_model_param(const vector<std::vector<double>>& calib_param);
        void predict(const Point2f& pupil_cent, const vector<Point2f>& point_crs, double eye_dist, Point2d& screen);
        
    private:
        vector<vector<double>> model_param;
        bool model_isready;
};

#endif //GAZE_MAPPING_H