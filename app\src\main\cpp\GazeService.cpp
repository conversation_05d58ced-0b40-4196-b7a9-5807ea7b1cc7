//
// Created by user on 2024/12/13.
//

#include "GazeService.h"

extern JavaVM *gJvm;
//给java层的回调函数
jobject gazeTrackCallback;

// 宏定义
#define ANATIVEWINDOW_RELEASE(window)   \
if (window) { \
            ANativeWindow_release(window); \
            window = 0; \
      }

GazeService::GazeService(const char * config_dir, const char* sn_serial): detector(config_dir){
    LOGI("------- create GazeSevice!!!! ------");
    /* 重复定义
    gaze_calibrator = GazeCalibrate();    // 校准器
    gaze_tracker = GazeTracker();         // 跟踪器
    pose_align_class = PoseAlign();       // 姿势校准器
    dataWriter = SaveVideos();            // 数据保存模块
    */

    sn_serial_number = sn_serial;
    config_filepath = config_dir + calibration_txtname;

    processing_state = service_state::SERVICE_NONE;        // 处理状态
    saving_videos = false;
    detect_result.clear();
    gaze_result.clear();
    pose_result.clear();
    calib_result.clear();
    gaze_params.clear();
}

GazeService::~GazeService() {
    LOGI("------- destropy GazeSevice!!!! ------");
    pthread_mutex_destroy(&mutex);
    ANATIVEWINDOW_RELEASE(window);
}

void GazeService::setANativeWindow(ANativeWindow *win) {
    pthread_mutex_lock(&mutex);
    // 替换问题
    ANATIVEWINDOW_RELEASE(this->window);
    this->window = win;
    pthread_mutex_unlock(&mutex);
}

/**
 * 眼动校准：校准坐标回调函数
 * @state 校准是否完成， @succeed 校准是否成功， @score 校准的评分
 * @calib_points_left/right 左右眼的各个点是否校准成功
 * @x，@y 当前校准点的坐标  @index 当前校准点的序号
 */
void onCalibrateCoordinate(bool state, bool succeed, float score, int calib_points_left, int calib_points_right, float x, float y, int index) {
    JNIEnv* env;
    if (gJvm->AttachCurrentThread(&env, nullptr) != JNI_OK) {
        return;
    }

    // 获取Java类及方法的信息
    jclass clazz = env->GetObjectClass(gazeTrackCallback);
    jmethodID methodId = env->GetMethodID(clazz, "onCalibrateCoordinate", "(ZZFIIFFI)V");

    // 调用Java对象的回调方法，传递数值
    env->CallVoidMethod(gazeTrackCallback, methodId, state, succeed, score, calib_points_left, calib_points_right, x, y, index);
}

/**
 * 当眼动追踪服务模式改变时回调
 * @param mode
 */
void onGazeServiceModeChange(int mode){
    JNIEnv* env;
    if (gJvm->AttachCurrentThread(&env, nullptr) != JNI_OK) {
        return;
    }

    // 获取Java类及方法的信息
    jclass clazz = env->GetObjectClass(gazeTrackCallback);
    jmethodID methodId = env->GetMethodID(clazz, "onGazeServiceModeChange", "(I)V");

    // 调用Java对象的回调方法，传递数值
    env->CallVoidMethod(gazeTrackCallback, methodId, mode);
}

/**
 * 绘制图片到屏幕
 * @param img
 */
void GazeService::draw(const Mat& img) {
    pthread_mutex_lock(&mutex);
    do {
        if (!window) {
            break;
        }
        // 设置window格式
        ANativeWindow_setBuffersGeometry(window, img.cols, img.rows,
                                         WINDOW_FORMAT_RGBA_8888);
        // 把需要显示的数据设置给buffer
        ANativeWindow_Buffer buffer;
        if (ANativeWindow_lock(window, &buffer, nullptr)) {
            ANATIVEWINDOW_RELEASE(window);
            break;
        }
        // 把视频数据刷新到buffer中
        auto *dstData = static_cast<uint8_t *>(buffer.bits);
        int dstlineSize = buffer.stride * 4;
        // 视频图形rgba数据
        uint8_t *srcData = img.data;
        int srclineSize = img.cols * 4;
        // 一行一行的拷贝
        for (int i = 0; i < buffer.height; ++i) {
            memcpy(dstData + i * dstlineSize, srcData + i * srclineSize, srclineSize);
        }
        // 提交渲染
        ANativeWindow_unlockAndPost(window);
    } while (false);
    pthread_mutex_unlock(&mutex);
}

string strRand(int length) {			// length: 产生字符串的长度
    char tmp;							// tmp: 暂存一个随机数
    string buffer;						// buffer: 保存返回值

    // 下面这两行比较重要:
    random_device rd;					// 产生一个 std::random_device 对象 rd
    default_random_engine random(rd());	// 用 rd 初始化一个随机数发生器 random

    for (int i = 0; i < length; i++) {
        tmp = random() % 36;	// 随机一个小于 36 的整数，0-9、A-Z 共 36 种字符
        if (tmp < 10) {			// 如果随机数小于 10，变换成一个阿拉伯数字的 ASCII
            tmp += '0';
        } else {				// 否则，变换成一个大写字母的 ASCII
            tmp -= 10;
            tmp += 'A';
        }
        buffer += tmp;
    }
    return buffer;
}
/*
 * =============== 属性参数更新&读取 ====================
 */


bool GazeService::get_tracker_state_func() const
{
    return gaze_tracker.get_calib_station_func();
}

void GazeService::update_tracker_config_func()
{
    if (fileExists(config_filepath)) {
        LOGI("file: GazeService, func: update_tracker_config_func, info: 配置文件存在。Loading...\n");
        if (readConfigFromFile(config_filepath, gaze_params)) {
            LOGI("file: GazeService, func: update_tracker_config_func, info: Config loaded successfully.");
            gaze_tracker.update_gaze_params(gaze_params);
        } else {
            LOGE("file: GazeService, func: update_tracker_config_func, info: Failed to load config.");
        }
    }
}

/*
 * =============== 命令函数 ====================
 */

/**
 * 设置眼动追踪服务回调
 */
void GazeService::setGazeTrackCallback(JNIEnv* env, jobject callback_calib)
{
    LOGE("GazeService::setGazeTrackCallback()");
    // 保存Java对象的引用为全局引用
    gazeTrackCallback = env->NewGlobalRef(callback_calib);
}

/**
 * 启动跟踪
 */
bool GazeService::start_tracking()
{
    bool state = false;
    LOGI("------- GazeCalibrate::start_tracking : processing_state = %s ------", processing_state ? "true" : "false");
    if(processing_state == service_state::SERVICE_NONE)
    {
        update_tracker_config_func();
        bool valid = gaze_tracker.get_calib_station_func();
        LOGI("------- GazeCalibrate::start_tracking : valid = %d ------", valid);
        if(valid)
        {
            processing_state = service_state::SERVICE_TRACKING;    // 切换到跟踪状态
            state = true;
            onGazeServiceModeChange(processing_state);
            pose_align_class.record_time = Clock::now();
        }
    }
    return state;
}

/**
 * 停止跟踪
 */
bool GazeService::stop_tracking()
{
    bool state = false;
    LOGI("------- GazeCalibrate::stop_tracking : processing_state = %s ------", processing_state ? "true" : "false");
    if(processing_state == service_state::SERVICE_TRACKING)
    {
        processing_state = service_state::SERVICE_NONE;           // 切换状态
        state = true;
        onGazeServiceModeChange(processing_state);
    }
    return state;
}

/**
 * 启动姿势校准
 */
bool GazeService::start_aligning_head_pose(bool calib)
{
    bool state = false;
    LOGI("------- GazeCalibrate::start_aligning_head_pose : processing_state = %s ------", processing_state ? "true" : "false");
    if(processing_state != service_state::SERVICE_POSE_ALIGN)
    {
        state = true;
        pose_result.clear();
        processing_state = service_state::SERVICE_POSE_ALIGN;         // 启动姿势校准
        pose_align_class.start_aligning_head_pose();      // 初始化姿势校准
        if(calib)
        {
            saving_videos = true;
            start_video_saving();            // 启动视频保存
        }
        onGazeServiceModeChange(processing_state);
    }
    return state;
}

/**
 * 结束姿势校准
 */
bool GazeService::stop_aligning_head_pose()
{
    bool state = false;
    LOGI("------- GazeCalibrate::stop_aligning_head_pose : processing_state = %s ------", processing_state ? "true" : "false");
    if(processing_state == service_state::SERVICE_POSE_ALIGN)
    {
        pose_align_class.stop_aligning_head_pose();     // 关闭姿势校准
        processing_state = service_state::SERVICE_NONE;
        state = true;
        pose_result.pose_aligned = true;       // 设置为完成状态

        if(saving_videos)
        {
            stop_video_saving();       // 关闭视频保存功能
            saving_videos = false;
        }
        onGazeServiceModeChange(processing_state);
    }
    return state;
}

/**
 * 启动校准
 */
bool GazeService::start_calibration()
{
    bool state = false;
    LOGI("------- GazeCalibrate::start_calibration : processing_state = %s ------", processing_state ? "true" : "false");
    if(processing_state == service_state::SERVICE_NONE)
    {
        processing_state = service_state::SERVICE_CALIBRATING;
        state = true;
        calib_result.clear();
        gaze_calibrator.start_calibration();     // 初始化校准
        onGazeServiceModeChange(processing_state);
    }
    return state;
}

/**
 * 中断校准
 */
bool GazeService::stop_calibration()
{
    bool state = false;
    LOGI("------- GazeCalibrate::stop_calibration : processing_state = %s ------", processing_state ? "true" : "false");
    if(processing_state == service_state::SERVICE_CALIBRATING)
    {
        processing_state = service_state::SERVICE_NONE;
        state = true;
        if(saving_videos)
        {
            LOGI("GazeService::stop_calibration finish calibration run stop_video_saving()!!!!");
            stop_video_saving();       // 中途关闭视频保存功能
            saving_videos = false;
        }

        if(!calib_result.finish_flag)
        {
            // 眼动校准未完成退出
            calib_result.finish_flag = true;
            gaze_calibrator.stop_calibration();      // 中途中断眼动校准
        }
        onGazeServiceModeChange(processing_state);
    }
    return state;
}

/**
 * 启动数据保存
 */
void GazeService::start_video_saving()
{
    string prefix_name;
    auto t = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
    std::stringstream ss;
    ss << std::put_time(std::localtime(&t), "_%Y_%m_%d_%H_%M_%S_");
    std::string str_time = ss.str();

    string rand_string_pre = strRand(12);

    string store_path = string(store_dir) + string(store_file_pre) + string(sn_serial_number);
    prefix_name = store_path +  str_time + rand_string_pre;

    LOGI("video filename prefix is %s!!!!", prefix_name.c_str());

    dataWriter.start_saving_videos(prefix_name);     // 开始数据保存
}

/**
 * 停止数据保存
 */
void GazeService::stop_video_saving()
{
    calibration_params_data_str calib_datas;
    bool flag = gaze_calibrator.get_calib_parameters_data(calib_datas);
    if(flag)
    {
        LOGI("GazeService::stop_video_saving run dataWriter.stop_saving_videos(calib_datas)!!!!");
        dataWriter.stop_saving_videos(calib_datas);
    }
}

/*
 * =============== 处理函数 ====================
 */
/**
 * 眼动跟踪
 */
gazetracker_result_str GazeService::process_gazetracker(const cv::Mat &Image)
{
    gaze_result.clear();    // 清空预测结果数据
    if(processing_state == service_state::SERVICE_TRACKING)
    {
        LOGI("GazeService::process_gazetracker: !!!!");
        detect_result = detector.detection(Image);
        gaze_result.valid = detect_result.left_eye.valid || detect_result.right_eye.valid;
        LOGI("GazeService::process_gazetracker detect_result.valid = %d !!!!", gaze_result.valid);
        gaze_point_str gaze_ = gaze_tracker.predict(detect_result);
        gaze_result = gaze_;     // 赋值 眼动坐标和距离
        gaze_result.skew = pose_align_class.recognize_pose_valid(detect_result, true);  // 判断姿势是否偏移
    }
    LOGI("GazeService::process_gazetracker: processing_state = %d, return gaze_result!!!!", processing_state);
    return gaze_result;
}

/**
 * 姿势校准
 */
pose_align_result GazeService::process_poseAlign(const cv::Mat &Image)
{
    LOGI("------- GazeCalibrate::process_poseAlign : processing_state = %d ------", processing_state);
    if(processing_state == service_state::SERVICE_POSE_ALIGN)
    {
        detect_result = detector.detection(Image);
        // 保存视频数据
        if(saving_videos)
        {
            dataWriter.save_videos_func(Image, detect_result);
        }
        pose_result = pose_align_class.head_pose_aligning(detect_result);
        LOGI("------- GazeCalibrate::process_poseAlign : pose_result.pose_aligned = %s ------", pose_result.pose_aligned ? "true" : "false");
        // 判断姿势校准是否完成
        if(pose_result.pose_aligned)
        {
            processing_state = service_state::SERVICE_NONE;
            onGazeServiceModeChange(processing_state);
        }
    }
    return pose_result;
}

/**
 * 眼动校准
 */
calibrating_result_str GazeService::process_calibration(const cv::Mat &Image)
{
    if(processing_state == service_state::SERVICE_CALIBRATING)
    {
        detect_result = detector.detection(Image);
        // 保存视频数据
        if(saving_videos)
        {
            dataWriter.save_videos_func(Image, detect_result);
        }
        calib_result = gaze_calibrator.run_calibration_func(detect_result, onCalibrateCoordinate);

        // 判断眼动校准是否完成
        if(calib_result.finish_flag)
        {
            // 保存校准数据以及更新眼动跟踪器的参数
            // *** 更新跟踪器的参数 ***
            calibration_params_data_str calib_datas;
            bool flag = gaze_calibrator.get_calib_parameters_data(calib_datas);
            if(flag)
            {
                gaze_params = calib_datas;
                writeConfigToFile(config_filepath, gaze_params);
                LOGI("GazeService::process_calibration run writeConfigToFile(%s, gaze_params);!!!!", config_filepath.c_str());
                LOGI("GazeService::process_calibration finish calibration left_params: !!!!");
                printf_2d_vector(gaze_params.left_params);
                LOGI("GazeService::process_calibration finish calibration right_params: !!!!");
                printf_2d_vector(gaze_params.right_params);
                gaze_tracker.update_gaze_params(gaze_params);
            }
            // *** 结束校准 ***
            LOGI("GazeService::process_calibration finish calibration run stop_calibration()!!!!");
            stop_calibration();
        }
    }
    return calib_result;
}

// =============== visual ====================

// ** poseAlign **
Mat GazeService::visual_pose_align_func(const cv::Mat &Image)
{
    cv::Mat img_rs, bgr_img, visual_img;
    cv::resize(Image, img_rs, cv::Size(visual_image_width, visual_image_height));
    cv::cvtColor(img_rs, bgr_img, COLOR_GRAY2BGR);
    visual_img = pose_align_class.draw_poseAlign_func(bgr_img, pose_result);
    return visual_img;
}

// ** Calibration **
Mat GazeService::visual_calibration_draw_func() const
{
    return gaze_calibrator.visual_calib_img;
}

// ** Tracker **
Mat GazeService::visual_tracker_result_func(const cv::Mat &Image)
{
    cv::Mat bgr_img, visual_img;
    cv::cvtColor(Image, bgr_img, COLOR_GRAY2BGR);
    detector.visual_detection_result_func(bgr_img);       // 显示检测中间结果

    cv::resize(bgr_img, visual_img, cv::Size(visual_image_width, visual_image_height));
    gaze_tracker.draw_predict_result_func(visual_img);
    return visual_img;
}
