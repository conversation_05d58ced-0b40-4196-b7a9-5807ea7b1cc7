//
// Created by user on 2024/12/12.
//

#ifndef MIT_DD_DEMO_DETECTION_H
#define MIT_DD_DEMO_DETECTION_H
#include "face_det.h"
#include "eye_track.h"
#include "lights_det.h"
#include "utils.h"

using namespace cv;
using namespace std;
using namespace chrono;


class Detection
{
public:
    explicit Detection(const char * config_dir);
    ~Detection();
private:
    string face_det_model_name = "/face_keypoints_mobileone_seg_heatmap_224x160_epoch60_0000753.rknn";
    string eye_tracking_model_name = "/ckpt_nir_eye_tracking_iris_mobileoneSeg_160x128_epoch27_iou951.rknn";
    string lights_det_model_name = "/pupil_nir_2lights_keypoints_mobileoneSeg_heatmap_128_epoch170_00028.rknn";
    string pupil_seg_model_name = "/ckpt_nir_refine_seg_iris_pupil_mobileone_s4_128_epoch31_iou972.rknn";

    cv::Rect left_eye_rect;                           // 【识别过程】左眼的跟踪框
    cv::Rect left_iris_rect;                          // 【识别过程】左眼的虹膜检测框
    cv::Point2f left_pupil_cent_point;                  // 【识别过程】左眼的瞳孔中心点
    std::vector<cv::Point2f> left_lights_point_list;    // 【识别过程】左眼的普尔钦斑
    cv::Rect right_eye_rect;                          // 【识别过程】右眼的跟踪框
    cv::Rect right_iris_rect;                         // 【识别过程】右眼的虹膜检测框
    cv::Point2f right_pupil_cent_point;                 // 【识别过程】右眼的瞳孔中心点
    std::vector<cv::Point2f> right_lights_point_list;   // 【识别过程】右眼的普尔钦斑

    float pose_distance;                             // 距离屏幕的位置 mm
    float record_interpupillary_distance;             // 记录精确的瞳距 pixel

    bool det_failed;      //【识别过程】检测失败标志

    // model
    faceDetect face_det;      // 【模型】人脸检测
    bool face_det_model_isready;
    eyeTrack eye_track_seg;   // 【模型】双眼跟踪
    bool eye_track_model_isready;
    lightsDetect lights_det;  // 【模型】普尔钦斑检测 & 瞳孔中心点检测
    bool lights_det_model_isready;

private:
    void reset_detection_parameters();
    static one_eye_detect_result assignment_func(std::vector<cv::Point2f> & lights_point, cv::Point2f & pupil_point, cv::Rect& iris);

public:
    frame_detection_result detection(const cv::Mat &Image);
    void visual_detection_result_func(cv::Mat &src);
};

#endif //MIT_DD_DEMO_DETECTION_H
