#ifndef UTILS_H
#define UTILS_H

#include <cstdio>
#include <string>
#include <sstream>
#include <iostream>
#include <fstream>
#include <cstdlib>
#include <cmath>
#include <chrono>
#include <android/log.h>

#if defined(__GNUC__) || defined(__GNUG__)
#include <experimental/filesystem>
namespace fs = std::experimental::filesystem;
#else
#include <filesystem>
    namespace fs = std::filesystem;
#endif

#include "opencv2/opencv.hpp"
#include <json/json.h>
#include <rknn_api.h>
#include <dirent.h>
#include <Eigen/Dense>
#include "timer.h"

using namespace std;
using namespace cv;
using namespace Eigen;

#define LOG_TAG "CPPLOG"

#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// 图像分辨率
#define image_width 4048
#define image_height 3040
// 图像中心点
#define image_cent_point_x 2024
#define image_cent_point_y 1520

// 屏幕显示分辨率
#define visual_image_width 1920
#define visual_image_height 1080

// 图像与屏幕显示的比例
#define screen_image_width_ratio 0.4743     // visual_image_width / image_width
#define screen_image_height_ratio 0.35526   // visual_image_height / image_height

// 姿势校准标准：眼睛位置和离屏幕距离
#define setting_pose_left_eye_x 750     //姿势校准阶段的UI参考图像坐标
#define setting_pose_left_eye_y 440
#define setting_pose_right_eye_x 1170
#define setting_pose_right_eye_y 440
#define setting_pose_iris_width 140      // pixel，设定的最佳离屏幕距离（50cm）时候的虹膜直径, new:140, old:110
#define setting_pose_center_dist 50      // cm，设定的最佳离屏幕距离

// 姿势校准标准：设定的眼睛可移动范围
#define pose_align_eye_radius 200
#define pose_align_interpupillary_distance_min 330        // 7.5mm@4.5cm@65cm
#define pose_align_interpupillary_distance_max 970        // 7.5mm@7.0cm@35cm
#define pose_align_iris_width_ratio_min_rate 0.85         // 60cm
#define pose_align_iris_width_ratio_min 110                // 60cm: new: 140 * 0.85 = 119, old: 110 * 0.85 = 93
#define pose_align_iris_width_ratio_max_rate 1.17         // 40cm
#define pose_align_iris_width_ratio_max 170               // 40cm: new: 140 * 1.17 = 164, old: 110 * 1.17 = 129
#define pupil_norm_distance_std 0.1                       // 判断连续值中的最大最小值的方差系数

#define two_eye_compare_iris_width_ratio_min 0.95   // 双眼虹膜直径比例-min
#define two_eye_compare_iris_width_ratio_max 1.05   // 双眼虹膜直径比例-max

#define pupil_distance_back_rate 0.1               // 校准过程中的限定范围
#define pupil_distance_leave_rate 0.2              // 跟踪过程中的活动范围

#define cunt_down_second_pose_align 2       // s
#define det_failed_tolerate_duration 10000    // ms 人脸检测丢失的容忍时长
#define pose_deviate_notify_min_time 10000  // ms 两次姿势偏移的最小时间间隔
#define pose_stable_move_min 50             // 判断人脸稳定的最小移动距离

#define two_eye_det_rect_ratio_min  0.5
#define two_eye_det_rect_ratio_max  2.0
#define iris_width_eye_distance_ratio_max  0.25
#define iris_width_eye_distance_ratio_min  0.10

#define blink_pupil_ratio_thres 0.8     // 判断瞳孔的形状，满足为圆形的比例阈值

#define refine_radius 7
#define refine_ratio 0.8
#define printf_num 5

/* 眼动应用处理函数的超参数*/
#define app_filter_thres 0.1        // 判断眼动的抖动阈值 px比例
#define app_merge_thres 0.025       // 合并注视点的最小距离 px比例
#define app_hit_near_thres 0.04     // 落到目标点附近的范围阈值

#define store_dir "/storage/emulated/0/Documents/"   //保存校准数据和参数的文件夹
#define store_file_pre "MIT_DD_tracker_"             // saving directory

typedef enum {
    SERVICE_NONE = 0,              /* 空 */
    SERVICE_TRACKING = 1,          /* 跟踪 */
    SERVICE_POSE_ALIGN = 2,        /* 姿势校准 */
    SERVICE_CALIBRATING = 3        /* 眼动校准 */
} service_state;

typedef enum {
    APP_NONE = 0,             /* 空 */
    APP_PQBLUR = 1,           /* 弱视/近视训练 */
    APP_READING = 2,          /* 阅读训练 */
    APP_STARE = 3,            /* 眼动能力检查 —— 注视 */
    APP_FOLLOW = 4,           /* 眼动能力检查 —— 追随 */
    APP_GLANCE = 5            /* 眼动能力检查 —— 扫视 */
} app_typename;

struct one_eye_detect_result  // 定义单只眼睛的检测结果结构
{
    bool  valid;          // 是否检测成功
    Point2f pupil;        // 瞳孔中心点
    Point2f light_left;   // 左光斑
    Point2f light_right;  // 右光斑
    cv::Rect iris_rect;  // 虹膜宽度
    one_eye_detect_result() : valid(false), pupil(Point2f(-1.0F, -1.0F)), light_left(Point2f(-1.0F, -1.0F)),
                              light_right(Point2f(-1.0F, -1.0F)), iris_rect(cv::Rect(-1, -1, 0, 0)){}
    //赋值运算符重载
    one_eye_detect_result& operator=(const one_eye_detect_result& other) {
        // 检查自我赋值
        if (this != &other) {
            valid = other.valid;
            pupil = other.pupil;
            light_left = other.light_left;
            light_right = other.light_right;
            iris_rect = other.iris_rect;
        }
        return *this;              // 返回当前对象的引用，支持链式赋值
    }

    // 初始化函数，用于重置所有元素
    void clear() {
        valid = false;
        pupil = Point2f(-1.0F, -1.0F);        // 清除所有元素
        light_left = Point2f(-1.0F, -1.0F);
        light_right = Point2f(-1.0F, -1.0F);
        iris_rect = cv::Rect(-1, -1, 0, 0);
    }
};

struct frame_detection_result  //定义一帧图像的检测结果
{
    one_eye_detect_result left_eye;
    one_eye_detect_result right_eye;
    float interpupillary_distance;
    frame_detection_result() : interpupillary_distance(0.0F){}

    // 初始化函数，用于清空所有元素
    void clear() {
        left_eye.clear(); // 清除所有元素
        right_eye.clear();
        interpupillary_distance = 0.0F;
    }
};

struct calib_state_struct //定义坐标结构体，包括两个坐标
{
    Point2f pupil;
    Point2f light_left;
    Point2f light_right;
    calib_state_struct(Point2f p, Point2f l, Point2f r) : pupil(p), light_left(l),  light_right(r){}
    calib_state_struct() : pupil(Point2f(-1.0F, -1.0F)), light_left(Point2f(-1.0F, -1.0F)), light_right(Point2f(-1.0F, -1.0F)) {}

    // 减法运算符重载
    float operator-(const calib_state_struct& other) const {
        float value;
        value = abs(pupil.x - other.pupil.x) +
                abs(pupil.y - other.pupil.y) +
                abs(light_left.x - other.light_left.x) +
                abs(light_left.y - other.light_left.y) +
                abs(light_right.x - other.light_right.x) +
                abs(light_right.y - other.light_right.y);
        return value;
    }
    //赋值运算符重载
    calib_state_struct& operator=(const calib_state_struct& other) {
        // 检查自我赋值
        if (this != &other) {
            pupil = other.pupil;
            light_left = other.light_left;
            light_right = other.light_right;
        }
        return *this;              // 返回当前对象的引用，支持链式赋值
    }

    // 初始化函数，用于重置所有元素
    void clear() {
        pupil = Point2f(-1.0F, -1.0F);        // 清除所有元素
        light_left = Point2f(-1.0F, -1.0F);
        light_right = Point2f(-1.0F, -1.0F);
    }
};

struct pose_pupil_points //定义姿势信息结构体
{
    Point2f left_pupil;
    Point2f right_pupil;
    pose_pupil_points(Point2f l, Point2f r) : left_pupil(l), right_pupil(r){}
    pose_pupil_points() : left_pupil(Point2f(-1.0F, -1.0F)), right_pupil(Point2f(-1.0F, -1.0F)){}
    //赋值运算符重载
    pose_pupil_points& operator=(const pose_pupil_points& other) {
        // 检查自我赋值
        if (this != &other) {
            left_pupil = other.left_pupil;
            right_pupil = other.right_pupil;
        }
        return *this;              // 返回当前对象的引用，支持链式赋值
    }
    //赋值运算符重载
    pose_pupil_points& operator=(const frame_detection_result& other) {
        // 检查自我赋值
        left_pupil = other.left_eye.pupil;
        right_pupil = other.right_eye.pupil;
        return *this;              // 返回当前对象的引用，支持链式赋值
    }
    // 减法运算符重载
    float operator-(const pose_pupil_points& other) const {
        float value;
        value = abs(left_pupil.x - other.left_pupil.x) +
                abs(left_pupil.y - other.left_pupil.y) +
                abs(right_pupil.x - other.right_pupil.x) +
                abs(right_pupil.y - other.right_pupil.y);
        return value;
    }
    // 初始化函数，用于重置所有元素
    void clear() {
        left_pupil = Point2f(-1.0F, -1.0F);        // 清除所有元素
        right_pupil = Point2f(-1.0F, -1.0F);
    }
};

struct pose_align_result //定义姿势校准结果结构体
{
    Point2f left_pupil;      // 左眼瞳孔位置,屏幕的坐标比例
    Point2f right_pupil;     // 右眼瞳孔位置,屏幕的坐标比例
    bool pose_aligned;       // 姿势校准完成
    bool aligning;           // 当前姿势是否符合要求
    float dist;              // 与设定距离的偏离 [0~1]
    int countdown;

    pose_align_result() : left_pupil(Point2f(-1.0F, -1.0F)), right_pupil(Point2f(-1.0F, -1.0F)), pose_aligned(false), aligning(false), dist(0.5F), countdown(0){}
    // 初始化函数，用于重置所有元素
    void clear() {
        left_pupil = Point2f(-1.0F, -1.0F);
        right_pupil = Point2f(-1.0F, -1.0F);
        pose_aligned = false;        // 清除所有元素
        aligning = false;
        dist = 0.5F;
        countdown = 0;
    }
};

struct calibrating_result_str //定义眼动校准过程结构体
{
    bool finish_flag;        // 眼动校准完成
    int left_consist_num;    // 左眼校准的进度
    int right_consist_num;   // 右眼校准的进度
    calibrating_result_str() : finish_flag(false), left_consist_num(0), right_consist_num(0){}
    calibrating_result_str(bool f, int l, int r) : finish_flag(f), left_consist_num(l), right_consist_num(r){}

    // 初始化函数，用于重置所有元素
    void clear() {
        finish_flag = false;        // 清除所有元素
        left_consist_num = 0;
        right_consist_num = 0;
    }
};

struct calibration_params_data_str //定义眼动校准过程的特征数据及映射参数的结构体
{
    std::vector<std::vector<double>> left_data;
    std::vector<std::vector<double>> left_params;
    std::vector<std::vector<double>> right_data;
    std::vector<std::vector<double>> right_params;
    calibration_params_data_str(): left_data({}),  left_params({}), right_data({}), right_params({}){}

    //赋值运算符重载
    calibration_params_data_str& operator=(const calibration_params_data_str& other) {
        // 检查自我赋值
        left_data = other.left_data;
        left_params = other.left_params;
        right_data = other.right_data;
        right_params = other.right_params;
        return *this;              // 返回当前对象的引用，支持链式赋值
    }

    // 初始化函数，用于清空 vector 中的所有元素
    void clear() {
        left_data.clear(); // 清除所有元素
        left_params.clear();
        right_data.clear();
        right_params.clear();
    }
};

struct mapping_params_str   // 2维视标映射参数
{
    bool valid;
    std::vector<std::vector<double>> left_params;
    std::vector<std::vector<double>> right_params;
    mapping_params_str() : valid(false), left_params({}), right_params({}){}

    // 初始化函数，用于清空 vector 中的所有元素
    void clear() {
        // 清除所有元素
        valid = false;
        left_params.clear();
        right_params.clear();
    }

    //赋值运算符重载
    mapping_params_str& operator=(const mapping_params_str& other) {
        // 检查自我赋值
        valid = other.valid;
        left_params = other.left_params;
        right_params = other.right_params;
        return *this;              // 返回当前对象的引用，支持链式赋值
    }

    mapping_params_str& operator=(const calibration_params_data_str& other) {
        // 检查自我赋值
        valid = true;
        left_params = other.left_params;
        right_params = other.right_params;
        return *this;              // 返回当前对象的引用，支持链式赋值
    }

};

struct gaze_trajectory_point       // 记录轨迹的结构体
{
    float gaze_x;     // 视标点-x
    float gaze_y;     // 视标点-y
    float duration;   // 持续时间
    gaze_trajectory_point() : gaze_x(0.0F), gaze_y(0.0F), duration(0.0F) {}
};

struct gaze_point_str       // 预测的眼动结果
{
    double gaze_x;     // 视标点-x
    double gaze_y;     // 视标点-y
    double dist;
    gaze_point_str() : gaze_x(0.0), gaze_y(0.0), dist(0.0) {}
    gaze_point_str(double x, double y, double d) : gaze_x(x), gaze_y(y), dist(d) {}
};

struct gazetracker_result_str       // 返回的眼动结果
{
    bool valid;       // 是否检测到用户
    bool skew;        // 姿势是否偏移 true-> 姿势发生偏转，false -> 姿势正常
    float gaze_x;     // 视标点-x [0~1]
    float gaze_y;     // 视标点-y [0~1]
    float dist;       // 距离 cm
    float duration;   // 注视的持续时间 ms

    gazetracker_result_str() : valid(false), skew(false), gaze_x(0.0F), gaze_y(0.0F), dist(0.0F), duration(0.0F) {}
    gazetracker_result_str(bool v, bool s, float x, float y, float d, float k) : valid(v), skew(s), gaze_x(x), gaze_y(y), dist(d), duration(k) {}

    //赋值运算符重载
    gazetracker_result_str& operator=(const gaze_point_str& other) {
        // 检查自我赋值
        gaze_x = (float)other.gaze_x;
        gaze_y = (float)other.gaze_y;
        dist = (float)other.dist;
        return *this;              // 返回当前对象的引用，支持链式赋值
    }

    // 初始化函数，用于清空 vector 中的所有元素
    void clear() {
        // 清除所有元素
        valid = false;
        skew = false;
        gaze_x = 0.0F;
        gaze_y = 0.0F;
        dist = 0.0F;
        duration = 0.0F;
    }
};

void check_ret(int ret, const char *msg);
int get_type_size(rknn_tensor_type type);

void find_best_point_by_prob_mask(cv::Mat &src, cv::Point& cent_point, float& maxval, int width, int height, float threshold);
void find_boundingRect_from_mask(cv::Mat &src, cv::Rect &bbox);
void get_mask_value_range(const cv::Mat &mask, float& min_val, float& max_val);

void create_eye_tracking_rect_func(cv::Rect &iris_rect, cv::Rect &search_rect, int imgw, int imgh);
void calculate_2points_state(const std::vector<cv::Point2f> &lights_point_list, vector<bool>& state_list);

void manual_threahold_float(const cv::Mat &src, cv::Mat& dst, float threshold, int set_value);
void manual_threahold_uchar(const cv::Mat &src, cv::Mat& dst, int threshold, int set_value);

int findMaxInPatch(const cv::Mat &img);
bool findForegroundCenter(const cv::Mat& mask, cv::Point2f& center);
cv::Point2f findForegroundCenter(const cv::Mat& mask);
void refine_lights_pixel(const cv::Mat &src, const cv::Point& ori_light_point, cv:: Point2f& light_point);

void rectangle_by_ratio(cv::Mat &img, cv::Rect bbox, const cv::Scalar& color, int thickness, float ratio_w, float ratio_h);
void circle_by_ratio(cv::Mat &img, cv::Point cp, const cv::Scalar& color, int thickness, int radius, float ratio_w, float ratio_h);

bool fileExists(const std::string& path);
bool checkVectorDimensions(const std::vector<std::vector<double>>& input_param);
void writeDebugParamsToFile(const std::string& filename,
                            const calibration_params_data_str& calib_datas);
void writeConfigToFile(const std::string& filename, mapping_params_str& map_params);
void writePoseToFile(const std::string& filename,
                     const cv::Point& left_p, const cv::Point& right_p, double dist);
bool readConfigFromFile(const std::string& filename, mapping_params_str& map_params);
bool readPoseFromFile(const std::string& filename,
                      cv::Point& left_p, cv::Point& right_p, double& dist);

std::vector<std::vector<double>> convertToDouble(const std::vector<std::vector<float>>& input);

void printf_2d_vector(const vector<vector<float>>& data);
void printf_2d_vector(const vector<vector<double>>& data);

void printf_1d_vector(const vector<float>& data);
void printf_1d_vector(const vector<double>& data);

void analysis_mean_deque(const deque<double>& data, double& Vmin, double& Vmax, double& Vmean);
void analysis_mean_point(const vector<cv::Point>& data, Point& Pmean);

// --- 估算人眼离屏幕的距离 ---
double map_iris_len_pixel_to_dist_func(double iris_width);
// Sigmoid function for distance judge.
double distance_sigmoid(double dist_ratio);

// 函数用来将数据写入文件
void saveDataToFile(const std::vector<std::vector<Point2f>>& data, const std::string& filename);
void saveDataToFile(const std::vector<Point>& data, const std::string& filename);

// 罗列指定路径的文件
void ListFilesInDirectory(const char *dirName, std::vector<std::string>& filenameList);
// 辅助函数，用于检查文件是否具有指定的扩展名
bool isSupportedType(const std::string& filename);

// 评估校准结果——计算拟合误差
float calculate_rmse(vector<vector<float>>& model_param, vector<vector<float>>& data_x, vector<vector<float>>& data_y);
float logistic_func(vector<float>& input_data);

void boolVectorToDecimal(const bool boolVec[], const int& number, int& decimal_left, int& decimal_right);

/* 将眼动轨迹的列表数据转化成json格式的string*/
string transfer_trajectory_to_jsondata(std::vector<std::vector<Point3f>>& stare_points);

#endif // UTILS_H