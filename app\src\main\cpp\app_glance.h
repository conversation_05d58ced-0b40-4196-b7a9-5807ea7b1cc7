//
// Created by user on 2024/12/30.
//

#ifndef MIT_DD_DEMO_APP_GLANCE_H
#define MIT_DD_DEMO_APP_GLANCE_H

#include "utils.h"

using namespace cv;
using namespace std;

class GazeGlance {
public:
    GazeGlance();
    ~GazeGlance();

public:
    std::vector<cv::Point3f> gaze_data_list;    // 应用期间采集的眼动数据
private:
    Point2f target_point;
    cv::Mat gaussian_mask;                      // 对注视结果进行后处理修正的高斯掩模
    //扫视当前点开始时间，毫秒
    long startTime;
    //扫视当前点完成时间，毫秒
    long endTime;

public:
    void reset_params_func();                                                   // 清空缓存数据
    bool set_target_point(float x, float y);                                    // 设置扫视的目标点
    bool collect_data(float x, float y, float duration);                        // 应用期间传输数据
    std::vector<std::vector<Point3f>> postprocess_trajectory_data();            // 处理眼动轨迹的数据
    string get_record_jsondata();                                               // 获取json的数据
};

#endif //MIT_DD_DEMO_APP_GLANCE_H
